<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة التضامن{% endblock %}</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    {% load static %}
    <link href="{% static 'css/style.css' %}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    {% if user.is_authenticated %}
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'dashboard' %}">
                <i class="fas fa-hands-helping me-2"></i>
                نظام التضامن
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'dashboard' %}">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    
                    {% if user.user_type == 'super_admin' %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-building me-1"></i>
                            المؤسسات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'organization_list' %}">
                                <i class="fas fa-list me-2"></i>قائمة جميع المؤسسات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><h6 class="dropdown-header">إنشاء مؤسسة جديدة</h6></li>
                            <li><a class="dropdown-item" href="{% url 'organization_create' %}">
                                <i class="fas fa-building me-2"></i>جمعية عادية
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'cyclic_organization_create' %}">
                                <i class="fas fa-sync-alt me-2"></i>جمعية دورية
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'family_fund_create' %}">
                                <i class="fas fa-piggy-bank me-2"></i>صندوق عائلة
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'manager_create' %}">
                                <i class="fas fa-user-plus me-2"></i>إضافة مدير
                            </a></li>
                        </ul>
                    </li>
                    {% endif %}

                    <!-- أنواع المؤسسات - للمالك فقط -->
                    {% if user.user_type == 'super_admin' %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-layer-group me-1"></i>
                            أنواع المؤسسات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'organization_list' %}?type=regular">
                                <i class="fas fa-building me-2"></i>الجمعيات العادية
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'cyclic_dashboard' %}">
                                <i class="fas fa-sync-alt me-2"></i>الجمعيات الدورية
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'family_fund_dashboard' %}">
                                <i class="fas fa-piggy-bank me-2"></i>صناديق العائلة
                            </a></li>
                        </ul>
                    </li>
                    {% endif %}
                    
                    {% if user.user_type in 'manager,super_admin' %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-users me-1"></i>
                            الأعضاء
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'member_list' %}">قائمة الأعضاء</a></li>
                            <li><a class="dropdown-item" href="{% url 'member_create' %}">إضافة عضو</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-money-bill-wave me-1"></i>
                            المالية
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'payment_list' %}">الدفعات</a></li>
                            <li><a class="dropdown-item" href="{% url 'payment_create' %}">إضافة دفعة</a></li>
                            <li><a class="dropdown-item" href="{% url 'expense_list' %}">المصاريف</a></li>
                            <li><a class="dropdown-item" href="{% url 'expense_create' %}">إضافة مصروف</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-bar me-1"></i>
                            التقارير
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'reports_dashboard' %}">
                                <i class="fas fa-chart-bar me-2"></i>لوحة التقارير
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'monthly_report' %}">
                                <i class="fas fa-calendar-alt me-2"></i>التقرير الشهري
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'member_status_report' %}">
                                <i class="fas fa-user-check me-2"></i>حالة الأعضاء
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'firebase_dashboard' %}">
                                <i class="fab fa-google me-2"></i>Firebase Dashboard
                            </a></li>
                        </ul>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            {{ user.get_full_name|default:user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">الملف الشخصي</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'logout' %}">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}

    <!-- Main Content -->
    <main class="container-fluid py-4">
        <!-- Messages -->
        {% if messages %}
        <div class="row">
            <div class="col-12">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Page Content -->
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0 text-muted">
                &copy; 2025 نظام إدارة التضامن. جميع الحقوق محفوظة.
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    {% load static %}
    <script src="{% static 'js/main.js' %}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
