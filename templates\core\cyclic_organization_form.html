{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users-cog me-2"></i>
                        {{ title }}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" id="cyclicOrgForm">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">
                                        <i class="fas fa-building me-1"></i>
                                        اسم الجمعية الدورية
                                    </label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="text-danger small">{{ form.name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.monthly_amount.id_for_label }}" class="form-label">
                                        <i class="fas fa-money-bill-wave me-1"></i>
                                        المبلغ الشهري (شيقل)
                                    </label>
                                    {{ form.monthly_amount }}
                                    {% if form.monthly_amount.errors %}
                                        <div class="text-danger small">{{ form.monthly_amount.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.cycle_duration_days.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-alt me-1"></i>
                                        مدة الدورة (بالأيام)
                                    </label>
                                    {{ form.cycle_duration_days }}
                                    {% if form.cycle_duration_days.errors %}
                                        <div class="text-danger small">{{ form.cycle_duration_days.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">عادة 30 يوم (شهر واحد)</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.cycle_start_date.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-check me-1"></i>
                                        تاريخ بداية الجمعية
                                    </label>
                                    {{ form.cycle_start_date }}
                                    {% if form.cycle_start_date.errors %}
                                        <div class="text-danger small">{{ form.cycle_start_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.phone.id_for_label }}" class="form-label">
                                        <i class="fas fa-phone me-1"></i>
                                        رقم الهاتف
                                    </label>
                                    {{ form.phone }}
                                    {% if form.phone.errors %}
                                        <div class="text-danger small">{{ form.phone.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.email.id_for_label }}" class="form-label">
                                        <i class="fas fa-envelope me-1"></i>
                                        البريد الإلكتروني
                                    </label>
                                    {{ form.email }}
                                    {% if form.email.errors %}
                                        <div class="text-danger small">{{ form.email.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                <i class="fas fa-align-left me-1"></i>
                                وصف الجمعية
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger small">{{ form.description.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة:</h6>
                            <ul class="mb-0">
                                <li>الجمعية الدورية هي نظام تقليدي يتم فيه جمع مبلغ شهري من كل عضو</li>
                                <li>يتم منح كامل المبلغ المجموع لأحد الأعضاء بالتناوب كل دورة</li>
                                <li>السحب يتم بشكل عشوائي من بين الأعضاء الذين دفعوا ولم يستلموا من قبل</li>
                                <li>يمكن إضافة الأعضاء بعد إنشاء الجمعية</li>
                            </ul>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'organization_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                رجوع
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                إنشاء الجمعية الدورية
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تعيين التاريخ الحالي كافتراضي
    const startDateField = document.getElementById('{{ form.cycle_start_date.id_for_label }}');
    if (startDateField && !startDateField.value) {
        const today = new Date().toISOString().split('T')[0];
        startDateField.value = today;
    }
});
</script>
{% endblock %}
