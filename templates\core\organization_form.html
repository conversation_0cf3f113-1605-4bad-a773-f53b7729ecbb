{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام إدارة التضامن{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="page-title">
                <i class="fas fa-building me-3"></i>
                {{ title }}
            </h1>
            <a href="{% url 'organization_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i>
                    معلومات المؤسسة
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="organizationForm" data-validate>
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label required">
                                <i class="fas fa-building me-2"></i>
                                {{ form.name.label }}
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                <i class="fas fa-align-left me-2"></i>
                                {{ form.description.label }}
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.description.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.address.id_for_label }}" class="form-label">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                {{ form.address.label }}
                            </label>
                            {{ form.address }}
                            {% if form.address.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.address.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.phone.id_for_label }}" class="form-label">
                                <i class="fas fa-phone me-2"></i>
                                {{ form.phone.label }}
                            </label>
                            {{ form.phone }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.phone.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">
                                <i class="fas fa-envelope me-2"></i>
                                {{ form.email.label }}
                            </label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    
                    <hr class="section-divider">
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'organization_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ المؤسسة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row justify-content-center mt-4">
    <div class="col-lg-8">
        <div class="card bg-light">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مهمة
                </h6>
                <ul class="mb-0">
                    <li>اسم المؤسسة مطلوب ولا يمكن تركه فارغاً</li>
                    <li>سيتم إنشاء اشتراك مجاني تلقائياً للمؤسسة الجديدة</li>
                    <li>يمكن إضافة مدير للمؤسسة بعد إنشائها</li>
                    <li>جميع البيانات يمكن تعديلها لاحقاً</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تركيز على حقل الاسم
        const nameField = document.getElementById('{{ form.name.id_for_label }}');
        if (nameField) {
            nameField.focus();
        }
        
        // تأثيرات بصرية للنموذج
        const form = document.getElementById('organizationForm');
        form.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
            submitBtn.disabled = true;
        });
    });
</script>
{% endblock %}
