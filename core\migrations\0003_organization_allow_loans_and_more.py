# Generated by Django 5.1.2 on 2025-07-01 22:31

import django.db.models.deletion
import django.utils.timezone
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_organization_current_cycle_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='organization',
            name='allow_loans',
            field=models.BooleanField(default=True, verbose_name='السماح بالقروض'),
        ),
        migrations.AddField(
            model_name='organization',
            name='emergency_fund_target',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='هدف صندوق الطوارئ'),
        ),
        migrations.AddField(
            model_name='organization',
            name='family_name',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='اسم العائلة'),
        ),
        migrations.AddField(
            model_name='organization',
            name='max_loan_amount',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='الحد الأقصى للقرض'),
        ),
        migrations.AddField(
            model_name='organization',
            name='monthly_contribution',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='المساهمة الشهرية المطلوبة'),
        ),
        migrations.AlterField(
            model_name='organization',
            name='organization_type',
            field=models.CharField(choices=[('regular', 'جمعية عادية'), ('cyclic', 'جمعية دورية'), ('family_fund', 'صندوق عائلة')], default='regular', max_length=20, verbose_name='نوع الجمعية'),
        ),
        migrations.CreateModel(
            name='FamilyLoan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('loan_type', models.CharField(choices=[('emergency', 'طوارئ'), ('education', 'تعليم'), ('medical', 'طبي'), ('business', 'تجاري'), ('personal', 'شخصي'), ('other', 'أخرى')], max_length=20, verbose_name='نوع القرض')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='مبلغ القرض')),
                ('purpose', models.TextField(verbose_name='الغرض من القرض')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('approved', 'موافق عليه'), ('rejected', 'مرفوض'), ('active', 'نشط'), ('completed', 'مكتمل'), ('overdue', 'متأخر')], default='pending', max_length=20, verbose_name='حالة القرض')),
                ('request_date', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ الطلب')),
                ('approval_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('due_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الاستحقاق')),
                ('repaid_amount', models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=10, verbose_name='المبلغ المسدد')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_loans', to=settings.AUTH_USER_MODEL, verbose_name='تمت الموافقة بواسطة')),
                ('member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='family_loans', to='core.member')),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='family_loans', to='core.organization')),
            ],
            options={
                'verbose_name': 'قرض عائلي',
                'verbose_name_plural': 'القروض العائلية',
                'ordering': ['-request_date'],
            },
        ),
        migrations.CreateModel(
            name='LoanRepayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='مبلغ السداد')),
                ('payment_date', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ السداد')),
                ('payment_method', models.CharField(choices=[('cash', 'نقدي'), ('bank_transfer', 'تحويل بنكي'), ('online', 'دفع إلكتروني'), ('check', 'شيك')], max_length=20, verbose_name='طريقة السداد')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('loan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='repayments', to='core.familyloan')),
                ('recorded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='سجل بواسطة')),
            ],
            options={
                'verbose_name': 'سداد قرض',
                'verbose_name_plural': 'سدادات القروض',
                'ordering': ['-payment_date'],
            },
        ),
    ]
