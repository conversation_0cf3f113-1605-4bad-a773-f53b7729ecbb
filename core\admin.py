from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import User, Organization, Member, Payment, Expense, ActivityLog, Subscription, CycleRound, CycleMember, FamilyLoan, LoanRepayment


@admin.register(User)
class CustomUserAdmin(UserAdmin):
    """إدارة المستخدمين"""
    list_display = ('username', 'email', 'first_name', 'last_name', 'user_type', 'organization', 'is_active')
    list_filter = ('user_type', 'is_active', 'organization')
    search_fields = ('username', 'email', 'first_name', 'last_name')

    fieldsets = UserAdmin.fieldsets + (
        ('معلومات إضافية', {
            'fields': ('user_type', 'organization', 'phone', 'is_active_user')
        }),
    )


@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    """إدارة المؤسسات"""
    list_display = ('name', 'organization_type', 'phone', 'email', 'is_active', 'created_at')
    list_filter = ('organization_type', 'is_active', 'created_at')
    search_fields = ('name', 'email', 'phone')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('المعلومات الأساسية', {
            'fields': ('name', 'description', 'organization_type')
        }),
        ('معلومات الاتصال', {
            'fields': ('address', 'phone', 'email')
        }),
        ('إعدادات الجمعية الدورية', {
            'fields': ('monthly_amount', 'cycle_duration_days', 'cycle_start_date', 'current_cycle', 'is_cycle_active'),
            'classes': ('collapse',)
        }),
        ('إعدادات صندوق العائلة', {
            'fields': ('family_name', 'emergency_fund_target', 'monthly_contribution', 'allow_loans', 'max_loan_amount'),
            'classes': ('collapse',)
        }),
        ('الحالة', {
            'fields': ('is_active',)
        }),
        ('التواريخ', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Member)
class MemberAdmin(admin.ModelAdmin):
    """إدارة الأعضاء"""
    list_display = ('name', 'organization', 'phone', 'city', 'is_active', 'created_at')
    list_filter = ('organization', 'city', 'is_active', 'created_at')
    search_fields = ('name', 'phone', 'city')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('المعلومات الأساسية', {
            'fields': ('organization', 'name', 'phone', 'city')
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
        ('الحالة', {
            'fields': ('is_active',)
        }),
        ('التواريخ', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    """إدارة الدفعات"""
    list_display = ('member', 'amount', 'payment_type', 'payment_method', 'payment_date', 'created_by')
    list_filter = ('payment_type', 'payment_method', 'payment_date', 'organization')
    search_fields = ('member__name', 'notes')
    readonly_fields = ('created_at', 'updated_at')
    date_hierarchy = 'payment_date'

    fieldsets = (
        ('معلومات الدفعة', {
            'fields': ('organization', 'member', 'amount', 'payment_type', 'payment_method', 'payment_date')
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Expense)
class ExpenseAdmin(admin.ModelAdmin):
    """إدارة المصاريف"""
    list_display = ('organization', 'expense_type', 'amount', 'beneficiary', 'expense_date', 'created_by')
    list_filter = ('expense_type', 'expense_date', 'organization')
    search_fields = ('beneficiary', 'description', 'notes')
    readonly_fields = ('created_at', 'updated_at')
    date_hierarchy = 'expense_date'

    fieldsets = (
        ('معلومات المصروف', {
            'fields': ('organization', 'expense_type', 'amount', 'expense_date', 'beneficiary')
        }),
        ('التفاصيل', {
            'fields': ('description', 'notes')
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Subscription)
class SubscriptionAdmin(admin.ModelAdmin):
    """إدارة الاشتراكات"""
    list_display = ('organization', 'subscription_type', 'status', 'max_members', 'current_members', 'remaining_slots', 'start_date', 'end_date')
    list_filter = ('subscription_type', 'status', 'start_date')
    search_fields = ('organization__name',)
    readonly_fields = ('created_at', 'updated_at', 'current_members', 'remaining_slots')

    fieldsets = (
        ('معلومات الاشتراك', {
            'fields': ('organization', 'subscription_type', 'status')
        }),
        ('التواريخ', {
            'fields': ('start_date', 'end_date')
        }),
        ('الحدود', {
            'fields': ('max_members', 'current_members', 'remaining_slots')
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def current_members(self, obj):
        """عدد الأعضاء الحاليين"""
        return obj.organization.get_active_members_count()
    current_members.short_description = "الأعضاء الحاليين"

    def remaining_slots(self, obj):
        """الأماكن المتبقية"""
        remaining = obj.organization.get_remaining_member_slots()
        if remaining == 0:
            return f"0 (مكتمل)"
        elif remaining <= 3:
            return f"{remaining} (قريب من الامتلاء)"
        else:
            return str(remaining)
    remaining_slots.short_description = "الأماكن المتبقية"


@admin.register(ActivityLog)
class ActivityLogAdmin(admin.ModelAdmin):
    """إدارة سجل الأنشطة"""
    list_display = ('user', 'action_type', 'model_name', 'description', 'created_at')
    list_filter = ('action_type', 'model_name', 'created_at', 'organization')
    search_fields = ('user__username', 'description')
    readonly_fields = ('created_at',)
    date_hierarchy = 'created_at'

    fieldsets = (
        ('معلومات النشاط', {
            'fields': ('user', 'organization', 'action_type', 'model_name', 'object_id')
        }),
        ('التفاصيل', {
            'fields': ('description', 'ip_address')
        }),
        ('التاريخ', {
            'fields': ('created_at',)
        }),
    )


@admin.register(CycleRound)
class CycleRoundAdmin(admin.ModelAdmin):
    """إدارة دورات الجمعية الدورية"""
    list_display = ('organization', 'round_number', 'status', 'winner_member', 'total_amount', 'start_date', 'end_date')
    list_filter = ('status', 'organization', 'start_date')
    search_fields = ('organization__name', 'winner_member__name')
    readonly_fields = ('created_at', 'updated_at')
    date_hierarchy = 'start_date'

    fieldsets = (
        ('معلومات الدورة', {
            'fields': ('organization', 'round_number', 'status')
        }),
        ('التواريخ', {
            'fields': ('start_date', 'end_date', 'draw_date')
        }),
        ('نتائج السحب', {
            'fields': ('winner_member', 'total_amount', 'is_amount_received')
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


class CycleMemberInline(admin.TabularInline):
    """إدارة أعضاء الدورة كـ inline"""
    model = CycleMember
    extra = 0
    readonly_fields = ('created_at', 'updated_at')
    fields = ('member', 'payment_status', 'paid_amount', 'payment_date', 'is_eligible_for_draw', 'has_received_before')


@admin.register(CycleMember)
class CycleMemberAdmin(admin.ModelAdmin):
    """إدارة أعضاء الدورات"""
    list_display = ('cycle_round', 'member', 'payment_status', 'paid_amount', 'payment_date', 'is_eligible_for_draw')
    list_filter = ('payment_status', 'is_eligible_for_draw', 'has_received_before', 'cycle_round__organization')
    search_fields = ('member__name', 'cycle_round__organization__name')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('معلومات العضو', {
            'fields': ('cycle_round', 'member')
        }),
        ('معلومات الدفع', {
            'fields': ('payment_status', 'paid_amount', 'payment_date')
        }),
        ('حالة العضو', {
            'fields': ('member_status', 'is_eligible_for_draw', 'has_received_before')
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


# إضافة inline للدورات في إدارة المؤسسات
class CycleRoundInline(admin.TabularInline):
    model = CycleRound
    extra = 0
    readonly_fields = ('created_at', 'updated_at')
    fields = ('round_number', 'status', 'start_date', 'end_date', 'winner_member', 'total_amount')


# تحديث إدارة المؤسسات لإضافة الدورات
OrganizationAdmin.inlines = [CycleRoundInline]

# تحديث إدارة الدورات لإضافة الأعضاء
CycleRoundAdmin.inlines = [CycleMemberInline]


@admin.register(FamilyLoan)
class FamilyLoanAdmin(admin.ModelAdmin):
    """إدارة القروض العائلية"""
    list_display = ('member', 'organization', 'loan_type', 'amount', 'status', 'request_date', 'due_date')
    list_filter = ('status', 'loan_type', 'organization', 'request_date')
    search_fields = ('member__name', 'organization__name', 'purpose')
    readonly_fields = ('created_at', 'updated_at')
    date_hierarchy = 'request_date'

    fieldsets = (
        ('معلومات القرض', {
            'fields': ('organization', 'member', 'loan_type', 'amount', 'purpose')
        }),
        ('حالة القرض', {
            'fields': ('status', 'approved_by')
        }),
        ('التواريخ', {
            'fields': ('request_date', 'approval_date', 'due_date')
        }),
        ('السداد', {
            'fields': ('repaid_amount',)
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('member', 'organization', 'approved_by')


class LoanRepaymentInline(admin.TabularInline):
    """إدارة سدادات القروض كـ inline"""
    model = LoanRepayment
    extra = 0
    readonly_fields = ('created_at',)
    fields = ('amount', 'payment_date', 'payment_method', 'recorded_by', 'notes')


@admin.register(LoanRepayment)
class LoanRepaymentAdmin(admin.ModelAdmin):
    """إدارة سدادات القروض"""
    list_display = ('loan', 'amount', 'payment_date', 'payment_method', 'recorded_by')
    list_filter = ('payment_method', 'payment_date', 'loan__organization')
    search_fields = ('loan__member__name', 'loan__organization__name')
    readonly_fields = ('created_at',)
    date_hierarchy = 'payment_date'

    fieldsets = (
        ('معلومات السداد', {
            'fields': ('loan', 'amount', 'payment_date', 'payment_method')
        }),
        ('تسجيل السداد', {
            'fields': ('recorded_by', 'notes')
        }),
        ('معلومات النظام', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('loan__member', 'loan__organization', 'recorded_by')


# إضافة inline للسدادات في إدارة القروض
FamilyLoanAdmin.inlines = [LoanRepaymentInline]


# تخصيص عنوان لوحة الإدارة
admin.site.site_header = "نظام إدارة التضامن"
admin.site.site_title = "إدارة التضامن"
admin.site.index_title = "لوحة التحكم"
