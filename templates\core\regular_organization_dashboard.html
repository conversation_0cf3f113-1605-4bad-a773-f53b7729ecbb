{% extends 'base.html' %}
{% load static %}
{% load currency_tags %}

{% block title %}لوحة تحكم الجمعية العادية - {{ organization.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="page-title">
                <i class="fas fa-building me-3 text-primary"></i>
                لوحة تحكم الجمعية العادية
            </h1>
            <p class="text-muted mb-0">
                مرحباً {{ user.get_full_name|default:user.username }}، مدير {{ organization.name }}
            </p>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي الأعضاء</h6>
                            <h3 class="mb-0">{{ stats.members_count }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي المدفوعات</h6>
                            <h3 class="mb-0">{{ stats.total_payments|format_currency }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي المصاريف</h6>
                            <h3 class="mb-0">{{ stats.total_expenses|format_currency }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-receipt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">الرصيد الحالي</h6>
                            <h3 class="mb-0">{{ stats.balance|format_currency }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-wallet fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الأعضاء المتأخرين -->
    {% if late_members %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        الأعضاء المتأخرين في الدفع
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>رقم الهاتف</th>
                                    <th>المدينة</th>
                                    <th>آخر دفعة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for member in late_members %}
                                <tr>
                                    <td>{{ member.name }}</td>
                                    <td>{{ member.phone }}</td>
                                    <td>{{ member.city }}</td>
                                    <td>
                                        {% with member.payments.last as last_payment %}
                                        {% if last_payment %}
                                        {{ last_payment.payment_date|date:"Y/m/d" }}
                                        {% else %}
                                        <span class="text-danger">لم يدفع بعد</span>
                                        {% endif %}
                                        {% endwith %}
                                    </td>
                                    <td>
                                        <a href="{% url 'member_detail' member_id=member.id %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- إجراءات سريعة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{% url 'member_create' %}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-user-plus me-2"></i>
                                إضافة عضو جديد
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{% url 'payment_create' %}" class="btn btn-outline-success w-100">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                تسجيل دفعة
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{% url 'expense_create' %}" class="btn btn-outline-danger w-100">
                                <i class="fas fa-receipt me-2"></i>
                                تسجيل مصروف
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{% url 'monthly_report' %}" class="btn btn-outline-info w-100">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقرير الشهري
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- آخر الأنشطة -->
    {% if recent_activities %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        آخر الأنشطة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        {% for activity in recent_activities %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">{{ activity.get_action_type_display }}</h6>
                                <p class="timeline-text">{{ activity.description }}</p>
                                <small class="text-muted">
                                    <i class="fas fa-user me-1"></i>{{ activity.user.get_full_name|default:activity.user.username }}
                                    <i class="fas fa-clock me-1 ms-3"></i>{{ activity.created_at|date:"Y/m/d H:i" }}
                                </small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- معلومات الجمعية -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الجمعية العادية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>اسم الجمعية:</strong> {{ organization.name }}</p>
                            <p><strong>الوصف:</strong> {{ organization.description|default:"غير محدد" }}</p>
                            <p><strong>الهاتف:</strong> {{ organization.phone|default:"غير محدد" }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>البريد الإلكتروني:</strong> {{ organization.email|default:"غير محدد" }}</p>
                            <p><strong>العنوان:</strong> {{ organization.address|default:"غير محدد" }}</p>
                            <p><strong>تاريخ الإنشاء:</strong> {{ organization.created_at|date:"Y/m/d" }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -31px;
    top: 15px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border-left: 3px solid #007bff;
}

.timeline-title {
    margin-bottom: 5px;
    color: #495057;
}

.timeline-text {
    margin-bottom: 10px;
    color: #6c757d;
}
</style>
{% endblock %}
