{% extends 'base.html' %}
{% load static %}
{% load currency_tags %}

{% block title %}لوحة تحكم الجمعيات الدورية{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>
                    <i class="fas fa-sync-alt me-2 text-primary"></i>
                    الجمعيات الدورية
                </h2>
                {% if user.user_type == 'super_admin' %}
                <a href="{% url 'cyclic_organization_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إنشاء جمعية دورية جديدة
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    {% if organization and cycle_stats %}
    <!-- إحصائيات الجمعية -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي الدورات</h6>
                            <h3 class="mb-0">{{ cycle_stats.total_rounds }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-list-ol fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">الدورات المكتملة</h6>
                            <h3 class="mb-0">{{ cycle_stats.completed_rounds }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي الأعضاء</h6>
                            <h3 class="mb-0">{{ cycle_stats.total_members }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">المبلغ الشهري</h6>
                            <h3 class="mb-0">{{ cycle_stats.monthly_amount|format_currency }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% if cycle_stats.current_round %}
    <!-- الدورة الحالية -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-play-circle me-2"></i>
                        الدورة الحالية - رقم {{ cycle_stats.current_round_number }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>الأعضاء الذين دفعوا</h6>
                                <h4 class="text-success">{{ cycle_stats.paid_members_count }}</h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>الأعضاء المتأخرين</h6>
                                <h4 class="text-danger">{{ cycle_stats.unpaid_members_count }}</h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>إجمالي المبلغ المجموع</h6>
                                <h4 class="text-primary">{{ cycle_stats.total_collected_current|format_currency }}</h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                {% if cycle_stats.can_draw %}
                                <a href="{% url 'cycle_round_detail' org_id=organization.id round_id=cycle_stats.current_round.id %}" 
                                   class="btn btn-success">
                                    <i class="fas fa-dice me-2"></i>
                                    إجراء السحب
                                </a>
                                {% else %}
                                <span class="text-muted">تم إجراء السحب</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    {% endif %}

    <!-- قائمة الجمعيات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        {% if user.user_type == 'super_admin' %}
                        جميع الجمعيات الدورية
                        {% else %}
                        جمعيتي الدورية
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if organizations %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>اسم الجمعية</th>
                                    <th>المبلغ الشهري</th>
                                    <th>عدد الأعضاء</th>
                                    <th>الدورة الحالية</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for org in organizations %}
                                <tr>
                                    <td>
                                        <strong>{{ org.name }}</strong>
                                        {% if org.description %}
                                        <br><small class="text-muted">{{ org.description|truncatechars:50 }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ org.monthly_amount|format_currency }}</td>
                                    <td>{{ org.get_total_members }}</td>
                                    <td>
                                        {% if org.current_cycle > 0 %}
                                        {{ org.current_cycle }}
                                        {% else %}
                                        <span class="text-muted">لم تبدأ</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if org.is_cycle_active %}
                                        <span class="badge bg-success">نشطة</span>
                                        {% else %}
                                        <span class="badge bg-secondary">متوقفة</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'cycle_members_list' org_id=org.id %}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-users"></i>
                                                الأعضاء
                                            </a>
                                            {% if org.can_start_new_cycle %}
                                            <a href="{% url 'cycle_round_create' org_id=org.id %}" 
                                               class="btn btn-sm btn-outline-success">
                                                <i class="fas fa-play"></i>
                                                بدء دورة
                                            </a>
                                            {% elif org.get_current_cycle_round %}
                                            <a href="{% url 'cycle_round_detail' org_id=org.id round_id=org.get_current_cycle_round.id %}" 
                                               class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                                الدورة الحالية
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد جمعيات دورية</h5>
                        {% if user.user_type == 'super_admin' %}
                        <p class="text-muted">ابدأ بإنشاء جمعية دورية جديدة</p>
                        <a href="{% url 'cyclic_organization_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إنشاء جمعية دورية
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
