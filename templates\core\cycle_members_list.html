{% extends 'base.html' %}
{% load static %}
{% load currency_tags %}

{% block title %}أعضاء {{ organization.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2>
                        <i class="fas fa-users me-2 text-primary"></i>
                        أعضاء الجمعية الدورية
                    </h2>
                    <p class="text-muted mb-0">{{ organization.name }}</p>
                </div>
                <div>
                    <a href="{% url 'member_create' %}" class="btn btn-success me-2">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة عضو جديد
                    </a>
                    <a href="{% url 'cyclic_dashboard' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        رجوع
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات -->
    {% if cycle_stats %}
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h6>إجمالي الأعضاء</h6>
                    <h3>{{ total_members }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h6>المبلغ الشهري</h6>
                    <h3>{{ organization.monthly_amount|format_currency }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h6>الدورات المكتملة</h6>
                    <h3>{{ cycle_stats.completed_rounds }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h6>الدورة الحالية</h6>
                    <h3>
                        {% if current_round %}
                        {{ current_round.round_number }}
                        {% else %}
                        لم تبدأ
                        {% endif %}
                    </h3>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- الدورة الحالية -->
    {% if current_round %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-play-circle me-2"></i>
                        الدورة الحالية - رقم {{ current_round.round_number }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>تاريخ البداية:</strong> {{ current_round.start_date|date:"Y/m/d" }}</p>
                            <p><strong>تاريخ النهاية:</strong> {{ current_round.end_date|date:"Y/m/d" }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>الحالة:</strong> 
                                {% if current_round.status == 'active' %}
                                <span class="badge bg-success">نشطة</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ current_round.get_status_display }}</span>
                                {% endif %}
                            </p>
                            {% if current_round.winner_member %}
                            <p><strong>الفائز:</strong> {{ current_round.winner_member.name }}</p>
                            {% endif %}
                        </div>
                    </div>
                    <div class="text-center">
                        <a href="{% url 'cycle_round_detail' org_id=organization.id round_id=current_round.id %}" 
                           class="btn btn-primary">
                            <i class="fas fa-eye me-2"></i>
                            عرض تفاصيل الدورة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- قائمة الأعضاء -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة الأعضاء
                    </h5>
                </div>
                <div class="card-body">
                    {% if members %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>رقم الهاتف</th>
                                    <th>المدينة</th>
                                    <th>تاريخ الانضمام</th>
                                    <th>عدد الدورات المشاركة</th>
                                    <th>استلم من قبل</th>
                                    <th>الحالة في الدورة الحالية</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for member in members %}
                                <tr>
                                    <td>
                                        <strong>{{ member.name }}</strong>
                                        {% if member.notes %}
                                        <br><small class="text-muted">{{ member.notes|truncatechars:30 }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ member.phone }}</td>
                                    <td>{{ member.city }}</td>
                                    <td>{{ member.created_at|date:"Y/m/d" }}</td>
                                    <td>{{ member.cycle_participations.count }}</td>
                                    <td>
                                        {% with member.cycle_participations.filter|length as received_count %}
                                        {% if received_count > 0 %}
                                        <span class="badge bg-success">{{ received_count }} مرة</span>
                                        {% else %}
                                        <span class="badge bg-secondary">لم يستلم</span>
                                        {% endif %}
                                        {% endwith %}
                                    </td>
                                    <td>
                                        {% if current_round %}
                                        {% with member.cycle_participations.filter as current_participation %}
                                        {% if current_participation %}
                                        {% with current_participation.first as participation %}
                                        {% if participation.payment_status == 'paid' %}
                                        <span class="badge bg-success">دفع</span>
                                        {% elif participation.payment_status == 'partial' %}
                                        <span class="badge bg-warning">دفع جزئي</span>
                                        {% else %}
                                        <span class="badge bg-danger">لم يدفع</span>
                                        {% endif %}
                                        {% endwith %}
                                        {% else %}
                                        <span class="badge bg-secondary">غير مشارك</span>
                                        {% endif %}
                                        {% endwith %}
                                        {% else %}
                                        <span class="text-muted">لا توجد دورة نشطة</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'member_detail' member_id=member.id %}" 
                                               class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'member_edit' member_id=member.id %}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-user-plus fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا يوجد أعضاء في الجمعية</h5>
                        <p class="text-muted">ابدأ بإضافة أعضاء للجمعية الدورية</p>
                        <a href="{% url 'member_create' %}" class="btn btn-primary">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة عضو جديد
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات إضافية -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات مهمة
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="mb-0">
                        <li>يجب على كل عضو دفع المبلغ الشهري ({{ organization.monthly_amount|format_currency }}) للمشاركة في السحب</li>
                        <li>السحب العشوائي يتم من بين الأعضاء الذين دفعوا ولم يستلموا من قبل</li>
                        <li>العضو الذي يستلم المبلغ لا يشارك في السحوبات التالية حتى يستلم جميع الأعضاء</li>
                        <li>يمكن إضافة أعضاء جدد في أي وقت، وسيشاركون في الدورات القادمة</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
