{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام إدارة التضامن{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="page-title">
                <i class="fas fa-receipt me-3"></i>
                {{ title }}
            </h1>
            <a href="{% url 'expense_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i>
                    معلومات المصروف
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="expenseForm" data-validate>
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.expense_type.id_for_label }}" class="form-label required">
                                <i class="fas fa-tag me-2"></i>
                                {{ form.expense_type.label }}
                            </label>
                            {{ form.expense_type }}
                            {% if form.expense_type.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.expense_type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.amount.id_for_label }}" class="form-label required">
                                <i class="fas fa-money-bill me-2"></i>
                                {{ form.amount.label }}
                            </label>
                            <div class="input-group">
                                {{ form.amount }}
                                <span class="input-group-text">شيقل</span>
                            </div>
                            {% if form.amount.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.amount.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.expense_date.id_for_label }}" class="form-label required">
                                <i class="fas fa-calendar me-2"></i>
                                {{ form.expense_date.label }}
                            </label>
                            {{ form.expense_date }}
                            {% if form.expense_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.expense_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.beneficiary.id_for_label }}" class="form-label required">
                                <i class="fas fa-user me-2"></i>
                                {{ form.beneficiary.label }}
                            </label>
                            {{ form.beneficiary }}
                            {% if form.beneficiary.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.beneficiary.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label required">
                                <i class="fas fa-align-left me-2"></i>
                                {{ form.description.label }}
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.description.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                <i class="fas fa-sticky-note me-2"></i>
                                {{ form.notes.label }}
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.notes.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    
                    <hr class="section-divider">
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'expense_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ المصروف
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row justify-content-center mt-4">
    <div class="col-lg-8">
        <div class="card bg-light">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مهمة
                </h6>
                <ul class="mb-0">
                    <li>جميع الحقول المطلوبة يجب تعبئتها</li>
                    <li>المبلغ يجب أن يكون أكبر من صفر</li>
                    <li>تاريخ المصروف يمكن أن يكون في الماضي أو الحاضر</li>
                    <li>يجب تحديد المستفيد من المصروف بوضوح</li>
                    <li>وصف المصروف مطلوب لتوضيح الغرض منه</li>
                    <li>سيتم تسجيل المصروف باسم المستخدم الحالي</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تعيين التاريخ الحالي كافتراضي
        const dateField = document.getElementById('{{ form.expense_date.id_for_label }}');
        if (dateField && !dateField.value) {
            const today = new Date().toISOString().split('T')[0];
            dateField.value = today;
        }
        
        // تركيز على حقل نوع المصروف
        const typeField = document.getElementById('{{ form.expense_type.id_for_label }}');
        if (typeField) {
            typeField.focus();
        }
        
        // تنسيق حقل المبلغ
        const amountField = document.getElementById('{{ form.amount.id_for_label }}');
        if (amountField) {
            amountField.addEventListener('input', function() {
                // إزالة الأحرف غير الرقمية والنقطة العشرية
                this.value = this.value.replace(/[^0-9.]/g, '');
                
                // التأكد من وجود نقطة عشرية واحدة فقط
                const parts = this.value.split('.');
                if (parts.length > 2) {
                    this.value = parts[0] + '.' + parts.slice(1).join('');
                }
            });
        }
        
        // تأثيرات بصرية للنموذج
        const form = document.getElementById('expenseForm');
        form.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
            submitBtn.disabled = true;
        });
    });
</script>
{% endblock %}
