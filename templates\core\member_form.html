{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام إدارة التضامن{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="page-title">
                <i class="fas fa-user-plus me-3"></i>
                {{ title }}
            </h1>
            <a href="{% url 'member_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    معلومات العضو
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="memberForm" data-validate>
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label required">
                                <i class="fas fa-user me-2"></i>
                                {{ form.name.label }}
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.phone.id_for_label }}" class="form-label required">
                                <i class="fas fa-phone me-2"></i>
                                {{ form.phone.label }}
                            </label>
                            {{ form.phone }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.phone.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.city.id_for_label }}" class="form-label required">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                {{ form.city.label }}
                            </label>
                            {{ form.city }}
                            {% if form.city.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.city.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                <i class="fas fa-sticky-note me-2"></i>
                                {{ form.notes.label }}
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.notes.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    
                    <hr class="section-divider">
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'member_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ العضو
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معلومات الحد الأقصى للأعضاء -->
{% if organization %}
<div class="row justify-content-center mt-4">
    <div class="col-lg-8">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    إحصائيات الأعضاء
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4">
                        <div class="stat-item">
                            <h4 class="text-primary">{{ current_members_count }}</h4>
                            <small class="text-muted">الأعضاء الحاليين</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-item">
                            <h4 class="text-success">{{ max_members_limit }}</h4>
                            <small class="text-muted">الحد الأقصى</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-item">
                            <h4 class="{% if remaining_slots > 0 %}text-warning{% else %}text-danger{% endif %}">{{ remaining_slots }}</h4>
                            <small class="text-muted">الأماكن المتبقية</small>
                        </div>
                    </div>
                </div>

                {% if remaining_slots <= 3 and remaining_slots > 0 %}
                <div class="alert alert-warning mt-3 mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تنبيه:</strong> يتبقى {{ remaining_slots }} أماكن فقط لإضافة أعضاء جدد.
                </div>
                {% elif remaining_slots == 0 %}
                <div class="alert alert-danger mt-3 mb-0">
                    <i class="fas fa-ban me-2"></i>
                    <strong>تحذير:</strong> لقد وصلت الحد الأقصى لعدد الأعضاء. للحصول على المزيد من الأماكن، يرجى التواصل مع مالك المشروع على الرقم <strong>0598455262</strong>.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- معلومات إضافية -->
<div class="row justify-content-center mt-4">
    <div class="col-lg-8">
        <div class="card bg-light">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مهمة
                </h6>
                <ul class="mb-0">
                    <li>اسم العضو ورقم الهاتف مطلوبان</li>
                    <li>رقم الهاتف يجب أن يكون فريداً في المؤسسة</li>
                    <li>يمكن إضافة ملاحظات إضافية عن العضو</li>
                    <li>بعد إنشاء العضو يمكن تسجيل دفعاته</li>
                    <li>جميع البيانات يمكن تعديلها لاحقاً</li>
                    {% if organization %}
                    <li><strong>الحد الأقصى للأعضاء:</strong> {{ max_members_limit }} عضو</li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تركيز على حقل الاسم
        const nameField = document.getElementById('{{ form.name.id_for_label }}');
        if (nameField) {
            nameField.focus();
        }
        
        // تنسيق رقم الهاتف
        const phoneField = document.getElementById('{{ form.phone.id_for_label }}');
        if (phoneField) {
            phoneField.addEventListener('input', function() {
                // إزالة الأحرف غير الرقمية
                this.value = this.value.replace(/[^0-9+]/g, '');
            });
        }
        
        // تأثيرات بصرية للنموذج
        const form = document.getElementById('memberForm');
        form.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
            submitBtn.disabled = true;
        });
    });
</script>
{% endblock %}
