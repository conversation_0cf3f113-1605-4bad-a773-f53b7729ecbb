from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.db.models import Sum, Count, Q
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
import json
from django.views.decorators.http import require_POST
from django.utils.http import urlencode

from .models import User, Organization, Member, Payment, Expense, ActivityLog, Subscription, CycleRound, CycleMember, FamilyLoan, LoanRepayment
from .forms import LoginForm, OrganizationForm, ManagerForm, MemberForm, PaymentForm, ExpenseForm, ReportFilterForm, CyclicOrganizationForm, CycleRoundForm, CycleMemberForm, FamilyFundForm, FamilyLoanForm, LoanRepaymentForm
from .utils import (
    super_admin_required, manager_required, organization_access_required,
    log_activity, get_client_ip, export_to_excel, export_to_pdf,
    get_monthly_stats, get_member_payment_status, create_new_cycle_round,
    perform_random_draw, get_cycle_statistics, update_cycle_member_payment
)


def login_view(request):
    """صفحة تسجيل الدخول"""
    if request.user.is_authenticated:
        return redirect('dashboard')

    if request.method == 'POST':
        form = LoginForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')

            # محاولة تسجيل الدخول بـ username أولاً
            user = authenticate(username=username, password=password)

            # إذا فشل، جرب بالبريد الإلكتروني
            if user is None:
                try:
                    user_obj = User.objects.get(email=username)
                    user = authenticate(username=user_obj.username, password=password)
                except User.DoesNotExist:
                    user = None

            if user is not None and user.is_active:
                login(request, user)

                # تسجيل النشاط
                log_activity(
                    user=user,
                    action_type='login',
                    description=f'تسجيل دخول المستخدم {user.username}',
                    ip_address=get_client_ip(request)
                )

                messages.success(request, f'مرحباً {user.get_full_name() or user.username}')
                return redirect('dashboard')
            else:
                messages.error(request, 'بيانات تسجيل الدخول غير صحيحة أو الحساب غير مفعل')
        else:
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{error}')
    else:
        form = LoginForm()

    return render(request, 'core/login.html', {'form': form})


def login_help_view(request):
    """صفحة معلومات تسجيل الدخول"""
    return render(request, 'core/login_help.html')


@login_required
def logout_view(request):
    """تسجيل الخروج"""
    # تسجيل النشاط
    log_activity(
        user=request.user,
        action_type='logout',
        description=f'تسجيل خروج المستخدم {request.user.username}',
        ip_address=get_client_ip(request)
    )

    logout(request)
    messages.success(request, 'تم تسجيل الخروج بنجاح')
    return redirect('login')


@login_required
def dashboard_view(request):
    """لوحة التحكم الرئيسية"""
    user = request.user

    # إحصائيات حسب نوع المستخدم
    if user.user_type == 'super_admin':
        # إحصائيات مالك المشروع
        stats = {
            'organizations_count': Organization.objects.filter(is_active=True).count(),
            'managers_count': User.objects.filter(user_type='manager', is_active=True).count(),
            'members_count': Member.objects.filter(is_active=True).count(),
            'total_payments': Payment.objects.aggregate(total=Sum('amount'))['total'] or Decimal('0'),
            'total_expenses': Expense.objects.aggregate(total=Sum('amount'))['total'] or Decimal('0'),
        }

        # الأعضاء المتأخرين من جميع المؤسسات
        late_members = Member.objects.filter(
            is_active=True,
            payments__isnull=True
        ).union(
            Member.objects.filter(
                is_active=True,
                payments__payment_date__lt=timezone.now().date() - timedelta(days=45)
            )
        )[:10]

        # آخر الأنشطة من جميع المؤسسات
        recent_activities = ActivityLog.objects.all()[:10]

    else:
        # إحصائيات مدير المؤسسة
        org = user.organization
        if not org:
            messages.error(request, 'لم يتم تعيين مؤسسة لحسابك')
            return redirect('login')

        stats = {
            'members_count': org.get_total_members(),
            'total_payments': org.payments.aggregate(total=Sum('amount'))['total'] or Decimal('0'),
            'total_expenses': org.expenses.aggregate(total=Sum('amount'))['total'] or Decimal('0'),
        }

        # الأعضاء المتأخرين من المؤسسة
        late_members = Member.objects.filter(
            organization=org,
            is_active=True,
            payments__isnull=True
        ).union(
            Member.objects.filter(
                organization=org,
                is_active=True,
                payments__payment_date__lt=timezone.now().date() - timedelta(days=45)
            )
        )[:10]

        # آخر الأنشطة من المؤسسة
        recent_activities = ActivityLog.objects.filter(organization=org)[:10]

    # حساب الرصيد
    stats['balance'] = stats['total_payments'] - stats['total_expenses']

    context = {
        'stats': stats,
        'late_members': late_members,
        'recent_activities': recent_activities,
    }

    return render(request, 'core/dashboard.html', context)


@super_admin_required
def organization_list_view(request):
    """قائمة المؤسسات"""
    organizations = Organization.objects.all().order_by('-created_at')

    context = {
        'organizations': organizations,
    }

    return render(request, 'core/organization_list.html', context)


@super_admin_required
def organization_create_view(request):
    """إنشاء مؤسسة جديدة"""
    if request.method == 'POST':
        form = OrganizationForm(request.POST)
        if form.is_valid():
            organization = form.save()

            # إنشاء اشتراك مجاني للمؤسسة
            Subscription.objects.create(
                organization=organization,
                subscription_type='free',
                status='active'
            )

            # تسجيل النشاط
            log_activity(
                user=request.user,
                action_type='create',
                model_name='Organization',
                object_id=organization.id,
                description=f'إنشاء مؤسسة جديدة: {organization.name}',
                ip_address=get_client_ip(request)
            )

            messages.success(request, f'تم إنشاء المؤسسة "{organization.name}" بنجاح')
            return redirect('organization_list')
    else:
        form = OrganizationForm()

    return render(request, 'core/organization_form.html', {
        'form': form,
        'title': 'إضافة مؤسسة جديدة'
    })


@super_admin_required
def manager_create_view(request):
    """إنشاء مدير جديد"""
    if request.method == 'POST':
        form = ManagerForm(request.POST)
        if form.is_valid():
            manager = form.save()

            # تسجيل النشاط
            log_activity(
                user=request.user,
                action_type='create',
                model_name='User',
                object_id=manager.id,
                description=f'إنشاء مدير جديد: {manager.get_full_name() or manager.username}',
                organization=manager.organization,
                ip_address=get_client_ip(request)
            )

            messages.success(request, f'تم إنشاء المدير "{manager.get_full_name() or manager.username}" بنجاح')
            return redirect('organization_list')
    else:
        form = ManagerForm()

    return render(request, 'core/manager_form.html', {
        'form': form,
        'title': 'إضافة مدير جديد'
    })


@manager_required
def member_list_view(request):
    """قائمة الأعضاء"""
    user = request.user

    if user.user_type == 'super_admin':
        members = Member.objects.all().order_by('-created_at')
        organization = None
    else:
        members = Member.objects.filter(organization=user.organization).order_by('-created_at')
        organization = user.organization

    # فلترة حسب الحالة
    status = request.GET.get('status')
    if status == 'active':
        members = members.filter(is_active=True)
    elif status == 'inactive':
        members = members.filter(is_active=False)

    # البحث
    search = request.GET.get('search')
    if search:
        members = members.filter(
            Q(name__icontains=search) |
            Q(phone__icontains=search) |
            Q(city__icontains=search)
        )

    # حساب الإحصائيات
    active_members = members.filter(is_active=True)
    late_members_count = 0
    total_payments = Decimal('0')

    # حساب الأعضاء المتأخرين وإجمالي الدفعات
    for member in active_members:
        payment_status = get_member_payment_status(member)
        if payment_status['status'] in ['متأخر', 'تحذير']:
            late_members_count += 1
        total_payments += member.get_total_payments()

    context = {
        'members': members,
        'search': search,
        'status': status,
        'organization': organization,
        'late_members_count': late_members_count,
        'total_payments': total_payments,
    }

    # إضافة معلومات الحد الأقصى للمدراء
    if organization:
        context.update({
            'current_members_count': organization.get_active_members_count(),
            'max_members_limit': organization.get_max_members_limit(),
            'remaining_slots': organization.get_remaining_member_slots(),
            'can_add_member': organization.can_add_member(),
        })

    return render(request, 'core/member_list.html', context)


@manager_required
def member_create_view(request):
    """إنشاء عضو جديد"""
    user = request.user
    organization = user.organization if user.user_type == 'manager' else None

    # التحقق من إمكانية إضافة عضو جديد قبل عرض النموذج
    if organization and not organization.can_add_member():
        messages.error(
            request,
            'لقد وصلت الحد الاقصى لعدد الاعضاء الرجاء التواصل مع مالك المشروع على الرقم 0598455262'
        )
        return redirect('member_list')

    if request.method == 'POST':
        form = MemberForm(request.POST, organization=organization)
        if form.is_valid():
            member = form.save(commit=False)
            if user.user_type == 'manager':
                member.organization = user.organization
            member.save()

            # تسجيل النشاط
            log_activity(
                user=request.user,
                action_type='create',
                model_name='Member',
                object_id=member.id,
                description=f'إنشاء عضو جديد: {member.name}',
                organization=member.organization,
                ip_address=get_client_ip(request)
            )

            messages.success(request, f'تم إنشاء العضو "{member.name}" بنجاح')
            return redirect('member_list')
    else:
        form = MemberForm(organization=organization)

    # إضافة معلومات الحد الأقصى للسياق
    context = {
        'form': form,
        'title': 'إضافة عضو جديد',
        'organization': organization,
    }

    if organization:
        context.update({
            'current_members_count': organization.get_active_members_count(),
            'max_members_limit': organization.get_max_members_limit(),
            'remaining_slots': organization.get_remaining_member_slots(),
        })

    return render(request, 'core/member_form.html', context)


@manager_required
def payment_list_view(request):
    """قائمة الدفعات"""
    user = request.user

    if user.user_type == 'super_admin':
        payments = Payment.objects.all().order_by('-payment_date')
    else:
        payments = Payment.objects.filter(organization=user.organization).order_by('-payment_date')

    # فلترة حسب التاريخ
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    if start_date:
        payments = payments.filter(payment_date__gte=start_date)
    if end_date:
        payments = payments.filter(payment_date__lte=end_date)

    # فلترة حسب العضو
    member_id = request.GET.get('member')
    if member_id:
        payments = payments.filter(member_id=member_id)

    # فلترة حسب نوع الدفعة
    payment_type = request.GET.get('payment_type')
    if payment_type:
        payments = payments.filter(payment_type=payment_type)

    context = {
        'payments': payments,
        'start_date': start_date,
        'end_date': end_date,
        'member_id': member_id,
        'payment_type': payment_type,
    }

    return render(request, 'core/payment_list.html', context)


@manager_required
def payment_create_view(request):
    """إنشاء دفعة جديدة"""
    user = request.user
    organization = user.organization if user.user_type == 'manager' else None

    if request.method == 'POST':
        form = PaymentForm(request.POST, organization=organization)
        if form.is_valid():
            payment = form.save(commit=False)
            if user.user_type == 'manager':
                payment.organization = user.organization
            payment.created_by = user
            payment.save()

            # تسجيل النشاط
            log_activity(
                user=request.user,
                action_type='create',
                model_name='Payment',
                object_id=payment.id,
                description=f'تسجيل دفعة جديدة: {payment.amount} شيقل للعضو {payment.member.name}',
                organization=payment.organization,
                ip_address=get_client_ip(request)
            )

            messages.success(request, f'تم تسجيل الدفعة بمبلغ {payment.amount} شيقل بنجاح')
            return redirect('payment_list')
    else:
        form = PaymentForm(organization=organization)

    return render(request, 'core/payment_form.html', {
        'form': form,
        'title': 'تسجيل دفعة جديدة'
    })


@manager_required
def expense_list_view(request):
    """قائمة المصاريف"""
    user = request.user

    if user.user_type == 'super_admin':
        expenses = Expense.objects.all().order_by('-expense_date')
    else:
        expenses = Expense.objects.filter(organization=user.organization).order_by('-expense_date')

    # فلترة حسب التاريخ
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    if start_date:
        expenses = expenses.filter(expense_date__gte=start_date)
    if end_date:
        expenses = expenses.filter(expense_date__lte=end_date)

    # فلترة حسب نوع المصروف
    expense_type = request.GET.get('expense_type')
    if expense_type:
        expenses = expenses.filter(expense_type=expense_type)

    context = {
        'expenses': expenses,
        'start_date': start_date,
        'end_date': end_date,
        'expense_type': expense_type,
    }

    return render(request, 'core/expense_list.html', context)


@manager_required
def expense_create_view(request):
    """إنشاء مصروف جديد"""
    user = request.user

    if request.method == 'POST':
        form = ExpenseForm(request.POST)
        if form.is_valid():
            expense = form.save(commit=False)
            if user.user_type == 'manager':
                expense.organization = user.organization
            expense.created_by = user
            expense.save()

            # تسجيل النشاط
            log_activity(
                user=request.user,
                action_type='create',
                model_name='Expense',
                object_id=expense.id,
                description=f'تسجيل مصروف جديد: {expense.amount} شيقل - {expense.get_expense_type_display()}',
                organization=expense.organization,
                ip_address=get_client_ip(request)
            )

            messages.success(request, f'تم تسجيل المصروف بمبلغ {expense.amount} شيقل بنجاح')
            return redirect('expense_list')
    else:
        form = ExpenseForm()

    return render(request, 'core/expense_form.html', {
        'form': form,
        'title': 'تسجيل مصروف جديد'
    })


@login_required
def firebase_dashboard_view(request):
    """لوحة تحكم Firebase"""
    from .firebase_config import firebase_service

    try:
        # الحصول على البيانات من Firebase
        firebase_stats = {
            'users_count': len(firebase_service.get_collection('users')),
            'organizations_count': len(firebase_service.get_collection('organizations')),
            'members_count': len(firebase_service.get_collection('members')),
            'payments_count': len(firebase_service.get_collection('payments')),
            'expenses_count': len(firebase_service.get_collection('expenses')),
        }

        # آخر الدفعات من Firebase
        recent_payments = firebase_service.get_collection('payments', limit=10)

        # آخر المصاريف من Firebase
        recent_expenses = firebase_service.get_collection('expenses', limit=10)

        context = {
            'firebase_stats': firebase_stats,
            'recent_payments': recent_payments,
            'recent_expenses': recent_expenses,
            'firebase_connected': True,
        }

    except Exception as e:
        context = {
            'firebase_connected': False,
            'error_message': str(e)
        }

    return render(request, 'core/firebase_dashboard.html', context)


# API Views للـ AJAX
@login_required
def api_stats_view(request):
    """API للإحصائيات"""
    user = request.user

    if user.user_type == 'super_admin':
        stats = {
            'organizations_count': Organization.objects.filter(is_active=True).count(),
            'managers_count': User.objects.filter(user_type='manager', is_active=True).count(),
            'members_count': Member.objects.filter(is_active=True).count(),
            'total_payments': float(Payment.objects.aggregate(total=Sum('amount'))['total'] or 0),
            'total_expenses': float(Expense.objects.aggregate(total=Sum('amount'))['total'] or 0),
        }
    else:
        org = user.organization
        if not org:
            return JsonResponse({'error': 'لم يتم تعيين مؤسسة'}, status=400)

        stats = {
            'members_count': org.get_total_members(),
            'total_payments': float(org.payments.aggregate(total=Sum('amount'))['total'] or 0),
            'total_expenses': float(org.expenses.aggregate(total=Sum('amount'))['total'] or 0),
        }

    stats['balance'] = stats['total_payments'] - stats['total_expenses']

    return JsonResponse(stats)


@login_required
def api_monthly_payments_view(request):
    """API للدفعات الشهرية"""
    from django.db.models import Sum
    from datetime import datetime, timedelta
    import calendar

    user = request.user

    # آخر 6 أشهر
    months_data = []
    for i in range(6):
        date = datetime.now() - timedelta(days=30*i)
        month_name = calendar.month_name[date.month]

        if user.user_type == 'super_admin':
            total = Payment.objects.filter(
                payment_date__year=date.year,
                payment_date__month=date.month
            ).aggregate(total=Sum('amount'))['total'] or 0
        else:
            total = Payment.objects.filter(
                organization=user.organization,
                payment_date__year=date.year,
                payment_date__month=date.month
            ).aggregate(total=Sum('amount'))['total'] or 0

        months_data.append({
            'month': f"{month_name} {date.year}",
            'total': float(total)
        })

    months_data.reverse()

    return JsonResponse({
        'labels': [item['month'] for item in months_data],
        'values': [item['total'] for item in months_data]
    })


@login_required
def api_payment_types_view(request):
    """API لأنواع الدفعات"""
    from django.db.models import Sum

    user = request.user

    if user.user_type == 'super_admin':
        payments = Payment.objects.all()
    else:
        payments = Payment.objects.filter(organization=user.organization)

    payment_types = {}
    for payment_type, display_name in Payment.PAYMENT_TYPES:
        total = payments.filter(payment_type=payment_type).aggregate(
            total=Sum('amount')
        )['total'] or 0
        if total > 0:
            payment_types[display_name] = float(total)

    return JsonResponse({
        'labels': list(payment_types.keys()),
        'values': list(payment_types.values())
    })


@manager_required
def member_detail_view(request, member_id):
    """عرض تفاصيل العضو"""
    user = request.user
    member = get_object_or_404(Member, id=member_id)

    # التحقق من الصلاحيات
    if user.user_type == 'manager' and member.organization != user.organization:
        messages.error(request, 'غير مصرح بعرض هذا العضو')
        return redirect('member_list')

    # الحصول على دفعات العضو
    payments = member.payments.all().order_by('-payment_date')

    # حساب الإحصائيات
    total_payments = member.get_total_payments()
    last_payment = member.get_last_payment()

    # حساب حالة الدفع
    payment_status = get_member_payment_status(member)

    context = {
        'member': member,
        'payments': payments,
        'total_payments': total_payments,
        'last_payment': last_payment,
        'payment_status': payment_status,
        'title': f'تفاصيل العضو: {member.name}',
    }

    return render(request, 'core/member_detail.html', context)


@manager_required
def member_edit_view(request, member_id):
    """تعديل معلومات العضو"""
    user = request.user
    member = get_object_or_404(Member, id=member_id)

    # التحقق من الصلاحيات
    if user.user_type == 'manager' and member.organization != user.organization:
        messages.error(request, 'غير مصرح بتعديل هذا العضو')
        return redirect('member_list')

    organization = user.organization if user.user_type == 'manager' else None

    if request.method == 'POST':
        form = MemberForm(request.POST, instance=member, organization=organization)
        if form.is_valid():
            updated_member = form.save()

            # تسجيل النشاط
            log_activity(
                user=request.user,
                action_type='update',
                model_name='Member',
                object_id=member.id,
                description=f'تعديل معلومات العضو: {updated_member.name}',
                organization=updated_member.organization,
                ip_address=get_client_ip(request)
            )

            messages.success(request, f'تم تحديث معلومات العضو "{updated_member.name}" بنجاح')
            return redirect('member_detail', member_id=member.id)
    else:
        form = MemberForm(instance=member, organization=organization)

    context = {
        'form': form,
        'member': member,
        'title': f'تعديل العضو: {member.name}',
        'organization': organization,
    }

    return render(request, 'core/member_form.html', context)


def member_delete_view(request, member_id):
    """حذف عضو بشكل فوري"""
    user = request.user
    member = get_object_or_404(Member, id=member_id)
    # Only allow super_admin or manager of the same org
    if user.user_type == 'super_admin' or (user.user_type == 'manager' and member.organization == user.organization):
        member.delete()
        log_activity(
            user=request.user,
            action_type='delete',
            model_name='Member',
            object_id=member_id,
            description=f'حذف العضو: {member.name}',
            organization=member.organization,
            ip_address=get_client_ip(request)
        )
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({'success': True})
        messages.success(request, f'تم حذف العضو "{member.name}" بنجاح')
    else:
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({'success': False, 'error': 'غير مصرح'})
        messages.error(request, 'غير مصرح بحذف هذا العضو')
    return redirect('member_list')


@login_required
def reports_dashboard(request):
    """لوحة التقارير الرئيسية"""
    user = request.user
    org = user.organization if hasattr(user, 'organization') else None

    # إحصائيات سريعة للتقارير
    filter_kwargs = {}
    if user.user_type == 'manager' and org:
        filter_kwargs['organization'] = org
    elif user.user_type == 'super_admin':
        # Super admin can see all data
        pass
    else:
        # Regular users shouldn't access reports
        messages.error(request, 'غير مصرح لك بالوصول إلى التقارير')
        return redirect('dashboard')

    # إحصائيات هذا الشهر
    current_month = timezone.now().date().replace(day=1)
    next_month = (current_month + timedelta(days=32)).replace(day=1)

    monthly_payments = Payment.objects.filter(
        payment_date__gte=current_month,
        payment_date__lt=next_month,
        **filter_kwargs
    )

    monthly_expenses = Expense.objects.filter(
        expense_date__gte=current_month,
        expense_date__lt=next_month,
        **filter_kwargs
    )

    # إحصائيات الأعضاء
    if org:
        total_members = org.get_total_members()
        active_members = Member.objects.filter(organization=org, is_active=True).count()
    else:
        total_members = Member.objects.filter(**filter_kwargs).count()
        active_members = Member.objects.filter(is_active=True, **filter_kwargs).count()

    monthly_payments_total = monthly_payments.aggregate(total=Sum('amount'))['total'] or Decimal('0')
    monthly_expenses_total = monthly_expenses.aggregate(total=Sum('amount'))['total'] or Decimal('0')
    net_amount = monthly_payments_total - monthly_expenses_total

    stats = {
        'monthly_payments_total': monthly_payments_total,
        'monthly_payments_count': monthly_payments.count(),
        'monthly_expenses_total': monthly_expenses_total,
        'monthly_expenses_count': monthly_expenses.count(),
        'net_amount': net_amount,
        'total_members': total_members,
        'active_members': active_members,
        'current_month': current_month,
    }

    return render(request, 'core/reports_dashboard.html', {
        'stats': stats,
        'org': org,
    })


@login_required
def monthly_report_view(request):
    user = request.user
    org = user.organization if hasattr(user, 'organization') else None
    filter_kwargs = {}
    if user.user_type == 'manager' and org:
        filter_kwargs['organization'] = org
    
    filter_form = ReportFilterForm(request.GET or None, organization=org)
    report_data = []
    if filter_form.is_valid():
        start_date = filter_form.cleaned_data.get('start_date')
        end_date = filter_form.cleaned_data.get('end_date')
        member = filter_form.cleaned_data.get('member')
        payment_type = filter_form.cleaned_data.get('payment_type')
        payments = Payment.objects.filter(**filter_kwargs)
        expenses = Expense.objects.filter(**filter_kwargs)
        if start_date:
            payments = payments.filter(payment_date__gte=start_date)
            expenses = expenses.filter(expense_date__gte=start_date)
        if end_date:
            payments = payments.filter(payment_date__lte=end_date)
            expenses = expenses.filter(expense_date__lte=end_date)
        if member:
            payments = payments.filter(member=member)
        if payment_type:
            payments = payments.filter(payment_type=payment_type)
        # Prepare report data
        for p in payments:
            report_data.append({
                'النوع': 'دفعة',
                'العضو': p.member.name,
                'المبلغ': p.amount,
                'التاريخ': p.payment_date,
                'نوع الدفعة': p.get_payment_type_display(),
                'ملاحظات': p.notes,
            })
        for e in expenses:
            report_data.append({
                'النوع': 'مصروف',
                'العضو': '-',
                'المبلغ': e.amount,
                'التاريخ': e.expense_date,
                'نوع الدفعة': e.get_expense_type_display() if hasattr(e, 'get_expense_type_display') else '-',
                'ملاحظات': e.notes,
            })
        # Sort by date
        report_data.sort(key=lambda x: x['التاريخ'], reverse=True)

        # حساب الإحصائيات
        total_payments = sum([float(row['المبلغ']) for row in report_data if row['النوع'] == 'دفعة'])
        total_expenses = sum([float(row['المبلغ']) for row in report_data if row['النوع'] == 'مصروف'])
        net_amount = total_payments - total_expenses

        payment_count = len([row for row in report_data if row['النوع'] == 'دفعة'])
        expense_count = len([row for row in report_data if row['النوع'] == 'مصروف'])

        summary_stats = {
            'total_payments': Decimal(str(total_payments)),
            'total_expenses': Decimal(str(total_expenses)),
            'net_amount': Decimal(str(net_amount)),
            'payment_count': payment_count,
            'expense_count': expense_count,
            'total_transactions': len(report_data),
        }
    else:
        summary_stats = None

    return render(request, 'core/monthly_report.html', {
        'filter_form': filter_form,
        'report_data': report_data,
        'summary_stats': summary_stats,
        'request': request,
    })


@login_required
def monthly_report_export_excel(request):
    user = request.user
    org = user.organization if hasattr(user, 'organization') else None
    filter_kwargs = {}
    if user.user_type == 'manager' and org:
        filter_kwargs['organization'] = org
    filter_form = ReportFilterForm(request.GET or None, organization=org)
    report_data = []
    if filter_form.is_valid():
        start_date = filter_form.cleaned_data.get('start_date')
        end_date = filter_form.cleaned_data.get('end_date')
        member = filter_form.cleaned_data.get('member')
        payment_type = filter_form.cleaned_data.get('payment_type')
        payments = Payment.objects.filter(**filter_kwargs)
        expenses = Expense.objects.filter(**filter_kwargs)
        if start_date:
            payments = payments.filter(payment_date__gte=start_date)
            expenses = expenses.filter(expense_date__gte=start_date)
        if end_date:
            payments = payments.filter(payment_date__lte=end_date)
            expenses = expenses.filter(expense_date__lte=end_date)
        if member:
            payments = payments.filter(member=member)
        if payment_type:
            payments = payments.filter(payment_type=payment_type)
        for p in payments:
            report_data.append({
                'النوع': 'دفعة',
                'العضو': p.member.name,
                'المبلغ': float(p.amount),
                'التاريخ': p.payment_date.strftime('%Y-%m-%d'),
                'نوع الدفعة': p.get_payment_type_display(),
                'ملاحظات': p.notes,
            })
        for e in expenses:
            report_data.append({
                'النوع': 'مصروف',
                'العضو': '-',
                'المبلغ': float(e.amount),
                'التاريخ': e.expense_date.strftime('%Y-%m-%d'),
                'نوع الدفعة': e.get_expense_type_display() if hasattr(e, 'get_expense_type_display') else '-',
                'ملاحظات': e.notes,
            })
        report_data.sort(key=lambda x: x['التاريخ'], reverse=True)
    return export_to_excel(report_data, 'monthly_report')


@login_required
def monthly_report_export_pdf(request):
    user = request.user
    org = user.organization if hasattr(user, 'organization') else None
    filter_kwargs = {}
    if user.user_type == 'manager' and org:
        filter_kwargs['organization'] = org
    filter_form = ReportFilterForm(request.GET or None, organization=org)
    report_data = []
    if filter_form.is_valid():
        start_date = filter_form.cleaned_data.get('start_date')
        end_date = filter_form.cleaned_data.get('end_date')
        member = filter_form.cleaned_data.get('member')
        payment_type = filter_form.cleaned_data.get('payment_type')
        payments = Payment.objects.filter(**filter_kwargs)
        expenses = Expense.objects.filter(**filter_kwargs)
        if start_date:
            payments = payments.filter(payment_date__gte=start_date)
            expenses = expenses.filter(expense_date__gte=start_date)
        if end_date:
            payments = payments.filter(payment_date__lte=end_date)
            expenses = expenses.filter(expense_date__lte=end_date)
        if member:
            payments = payments.filter(member=member)
        if payment_type:
            payments = payments.filter(payment_type=payment_type)
        for p in payments:
            report_data.append({
                'النوع': 'دفعة',
                'العضو': p.member.name,
                'المبلغ': float(p.amount),
                'التاريخ': p.payment_date.strftime('%Y-%m-%d'),
                'نوع الدفعة': p.get_payment_type_display(),
                'ملاحظات': p.notes,
            })
        for e in expenses:
            report_data.append({
                'النوع': 'مصروف',
                'العضو': '-',
                'المبلغ': float(e.amount),
                'التاريخ': e.expense_date.strftime('%Y-%m-%d'),
                'نوع الدفعة': e.get_expense_type_display() if hasattr(e, 'get_expense_type_display') else '-',
                'ملاحظات': e.notes,
            })
        report_data.sort(key=lambda x: x['التاريخ'], reverse=True)
    return export_to_pdf(report_data, 'monthly_report', title='التقرير الشهري')


@login_required
def member_status_report(request):
    """تقرير حالة الأعضاء"""
    user = request.user
    org = user.organization if hasattr(user, 'organization') else None

    # التحقق من الصلاحيات
    if user.user_type not in ['manager', 'super_admin']:
        messages.error(request, 'غير مصرح لك بالوصول إلى التقارير')
        return redirect('dashboard')

    filter_kwargs = {}
    if user.user_type == 'manager' and org:
        filter_kwargs['organization'] = org

    # الحصول على جميع الأعضاء النشطين
    members = Member.objects.filter(is_active=True, **filter_kwargs).order_by('name')

    # إعداد بيانات التقرير
    report_data = []
    for member in members:
        payment_status = get_member_payment_status(member)
        last_payment = member.get_last_payment()
        total_payments = member.get_total_payments()

        report_data.append({
            'member': member,
            'last_payment': last_payment,
            'total_payments': total_payments,
            'status': payment_status['status'],
            'status_class': payment_status['status_class'],
            'days_since_payment': payment_status.get('days_since_payment', 0),
        })

    # إحصائيات سريعة
    total_members = len(report_data)
    regular_members = len([m for m in report_data if m['status'] == 'منتظم'])
    warning_members = len([m for m in report_data if m['status'] == 'تحذير'])
    late_members = len([m for m in report_data if m['status'] == 'متأخر'])

    stats = {
        'total_members': total_members,
        'regular_members': regular_members,
        'warning_members': warning_members,
        'late_members': late_members,
        'regular_percentage': (regular_members / total_members * 100) if total_members > 0 else 0,
        'warning_percentage': (warning_members / total_members * 100) if total_members > 0 else 0,
        'late_percentage': (late_members / total_members * 100) if total_members > 0 else 0,
    }

    return render(request, 'core/member_status_report.html', {
        'report_data': report_data,
        'stats': stats,
        'org': org,
    })


@login_required
def member_status_export_excel(request):
    """تصدير تقرير حالة الأعضاء إلى Excel"""
    user = request.user
    org = user.organization if hasattr(user, 'organization') else None

    if user.user_type not in ['manager', 'super_admin']:
        messages.error(request, 'غير مصرح لك بالوصول إلى التقارير')
        return redirect('dashboard')

    filter_kwargs = {}
    if user.user_type == 'manager' and org:
        filter_kwargs['organization'] = org

    members = Member.objects.filter(is_active=True, **filter_kwargs).order_by('name')

    export_data = []
    for member in members:
        payment_status = get_member_payment_status(member)
        last_payment = member.get_last_payment()
        total_payments = member.get_total_payments()

        export_data.append({
            'اسم العضو': member.name,
            'رقم الهاتف': member.phone,
            'البريد الإلكتروني': member.email,
            'الحالة': payment_status['status'],
            'آخر دفعة': last_payment.payment_date.strftime('%Y-%m-%d') if last_payment else 'لا توجد',
            'مبلغ آخر دفعة': float(last_payment.amount) if last_payment else 0,
            'إجمالي الدفعات': float(total_payments),
            'أيام منذ آخر دفعة': payment_status.get('days_since_payment', 0),
        })

    return export_to_excel(export_data, 'member_status_report', 'تقرير حالة الأعضاء')


@login_required
def member_status_export_pdf(request):
    """تصدير تقرير حالة الأعضاء إلى PDF"""
    user = request.user
    org = user.organization if hasattr(user, 'organization') else None

    if user.user_type not in ['manager', 'super_admin']:
        messages.error(request, 'غير مصرح لك بالوصول إلى التقارير')
        return redirect('dashboard')

    filter_kwargs = {}
    if user.user_type == 'manager' and org:
        filter_kwargs['organization'] = org

    members = Member.objects.filter(is_active=True, **filter_kwargs).order_by('name')

    export_data = []
    for member in members:
        payment_status = get_member_payment_status(member)
        last_payment = member.get_last_payment()
        total_payments = member.get_total_payments()

        export_data.append({
            'اسم العضو': member.name,
            'رقم الهاتف': member.phone,
            'البريد الإلكتروني': member.email,
            'الحالة': payment_status['status'],
            'آخر دفعة': last_payment.payment_date.strftime('%Y-%m-%d') if last_payment else 'لا توجد',
            'مبلغ آخر دفعة': float(last_payment.amount) if last_payment else 0,
            'إجمالي الدفعات': float(total_payments),
            'أيام منذ آخر دفعة': payment_status.get('days_since_payment', 0),
        })

    return export_to_pdf(export_data, 'member_status_report', 'تقرير حالة الأعضاء')


# ========== views الجمعية الدورية ==========

@super_admin_required
def cyclic_organization_create_view(request):
    """إنشاء جمعية دورية جديدة"""
    if request.method == 'POST':
        form = CyclicOrganizationForm(request.POST)
        if form.is_valid():
            organization = form.save()

            # إنشاء اشتراك مجاني للجمعية
            Subscription.objects.create(
                organization=organization,
                subscription_type='free',
                status='active'
            )

            # تسجيل النشاط
            log_activity(
                user=request.user,
                action_type='create',
                model_name='Organization',
                object_id=organization.id,
                description=f'إنشاء جمعية دورية جديدة: {organization.name}',
                ip_address=get_client_ip(request)
            )

            messages.success(request, f'تم إنشاء الجمعية الدورية "{organization.name}" بنجاح')
            return redirect('organization_list')
    else:
        form = CyclicOrganizationForm()

    return render(request, 'core/cyclic_organization_form.html', {
        'form': form,
        'title': 'إنشاء جمعية دورية جديدة'
    })


@manager_required
def cyclic_dashboard_view(request):
    """لوحة تحكم الجمعية الدورية"""
    user = request.user

    if user.user_type == 'super_admin':
        # مالك المشروع يمكنه رؤية جميع الجمعيات الدورية
        organizations = Organization.objects.filter(organization_type='cyclic', is_active=True)
        organization = None
    else:
        # المدير يرى جمعيته فقط
        organization = user.organization
        if not organization or not organization.is_cyclic_organization():
            messages.error(request, 'حسابك غير مرتبط بجمعية دورية')
            return redirect('dashboard')
        organizations = [organization]

    context = {
        'organizations': organizations,
        'organization': organization,
        'is_cyclic_view': True,
    }

    # إضافة إحصائيات للجمعية المحددة
    if organization:
        stats = get_cycle_statistics(organization)
        context['cycle_stats'] = stats

    return render(request, 'core/cyclic_dashboard.html', context)


@manager_required
def cycle_round_create_view(request, org_id):
    """إنشاء دورة جديدة"""
    organization = get_object_or_404(Organization, id=org_id)

    # التحقق من الصلاحيات
    if request.user.user_type == 'manager' and request.user.organization != organization:
        messages.error(request, 'ليس لديك صلاحية للوصول لهذه الجمعية')
        return redirect('cyclic_dashboard')

    if not organization.is_cyclic_organization():
        messages.error(request, 'هذه ليست جمعية دورية')
        return redirect('organization_list')

    if not organization.can_start_new_cycle():
        messages.error(request, 'لا يمكن بدء دورة جديدة. تأكد من عدم وجود دورة نشطة ووجود أعضاء كافيين')
        return redirect('cyclic_dashboard')

    # إنشاء الدورة الجديدة
    cycle_round = create_new_cycle_round(organization)

    if cycle_round:
        # تسجيل النشاط
        log_activity(
            user=request.user,
            action_type='create',
            model_name='CycleRound',
            object_id=cycle_round.id,
            description=f'إنشاء دورة جديدة رقم {cycle_round.round_number} للجمعية {organization.name}',
            organization=organization,
            ip_address=get_client_ip(request)
        )

        messages.success(request, f'تم إنشاء الدورة رقم {cycle_round.round_number} بنجاح')
    else:
        messages.error(request, 'حدث خطأ في إنشاء الدورة')

    return redirect('cycle_round_detail', org_id=org_id, round_id=cycle_round.id if cycle_round else 0)


@manager_required
def cycle_round_detail_view(request, org_id, round_id):
    """تفاصيل الدورة"""
    organization = get_object_or_404(Organization, id=org_id)
    cycle_round = get_object_or_404(CycleRound, id=round_id, organization=organization)

    # التحقق من الصلاحيات
    if request.user.user_type == 'manager' and request.user.organization != organization:
        messages.error(request, 'ليس لديك صلاحية للوصول لهذه الجمعية')
        return redirect('cyclic_dashboard')

    # الحصول على أعضاء الدورة
    cycle_members = cycle_round.cycle_members.select_related('member').order_by('member__name')

    # إحصائيات الدورة
    paid_count = cycle_members.filter(payment_status='paid').count()
    unpaid_count = cycle_members.filter(payment_status='not_paid').count()
    total_collected = cycle_members.filter(payment_status='paid').aggregate(
        total=Sum('paid_amount')
    )['total'] or Decimal('0')

    context = {
        'organization': organization,
        'cycle_round': cycle_round,
        'cycle_members': cycle_members,
        'paid_count': paid_count,
        'unpaid_count': unpaid_count,
        'total_collected': total_collected,
        'can_draw': paid_count > 0 and not cycle_round.winner_member,
    }

    return render(request, 'core/cycle_round_detail.html', context)


@manager_required
@require_POST
def perform_draw_view(request, org_id, round_id):
    """إجراء السحب العشوائي"""
    organization = get_object_or_404(Organization, id=org_id)
    cycle_round = get_object_or_404(CycleRound, id=round_id, organization=organization)

    # التحقق من الصلاحيات
    if request.user.user_type == 'manager' and request.user.organization != organization:
        return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية'})

    # التحقق من إمكانية السحب
    if cycle_round.winner_member:
        return JsonResponse({'success': False, 'message': 'تم إجراء السحب مسبقاً'})

    paid_members = cycle_round.cycle_members.filter(payment_status='paid').count()
    if paid_members == 0:
        return JsonResponse({'success': False, 'message': 'لا يوجد أعضاء دفعوا للمشاركة في السحب'})

    # إجراء السحب
    winner = perform_random_draw(cycle_round)

    if winner:
        # تسجيل النشاط
        log_activity(
            user=request.user,
            action_type='update',
            model_name='CycleRound',
            object_id=cycle_round.id,
            description=f'إجراء سحب عشوائي - الفائز: {winner.name} - الدورة {cycle_round.round_number}',
            organization=organization,
            ip_address=get_client_ip(request)
        )

        return JsonResponse({
            'success': True,
            'winner_name': winner.name,
            'winner_phone': winner.phone,
            'total_amount': float(cycle_round.total_amount)
        })
    else:
        return JsonResponse({'success': False, 'message': 'لا يوجد أعضاء مؤهلين للسحب'})


@manager_required
@require_POST
def update_member_payment_view(request, org_id, round_id, member_id):
    """تحديث دفعة عضو"""
    organization = get_object_or_404(Organization, id=org_id)
    cycle_round = get_object_or_404(CycleRound, id=round_id, organization=organization)
    cycle_member = get_object_or_404(CycleMember, cycle_round=cycle_round, member_id=member_id)

    # التحقق من الصلاحيات
    if request.user.user_type == 'manager' and request.user.organization != organization:
        return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية'})

    try:
        amount = Decimal(request.POST.get('amount', '0'))
        payment_date = request.POST.get('payment_date')

        if payment_date:
            payment_date = datetime.strptime(payment_date, '%Y-%m-%d').date()

        # تحديث الدفعة
        update_cycle_member_payment(cycle_member, amount, payment_date)

        # تسجيل النشاط
        log_activity(
            user=request.user,
            action_type='update',
            model_name='CycleMember',
            object_id=cycle_member.id,
            description=f'تحديث دفعة العضو {cycle_member.member.name} - المبلغ: {amount}',
            organization=organization,
            ip_address=get_client_ip(request)
        )

        return JsonResponse({
            'success': True,
            'payment_status': cycle_member.get_payment_status_display(),
            'paid_amount': float(cycle_member.paid_amount),
            'is_eligible': cycle_member.is_eligible_for_draw
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@manager_required
def cycle_members_list_view(request, org_id):
    """قائمة أعضاء الجمعية الدورية"""
    organization = get_object_or_404(Organization, id=org_id)

    # التحقق من الصلاحيات
    if request.user.user_type == 'manager' and request.user.organization != organization:
        messages.error(request, 'ليس لديك صلاحية للوصول لهذه الجمعية')
        return redirect('cyclic_dashboard')

    if not organization.is_cyclic_organization():
        messages.error(request, 'هذه ليست جمعية دورية')
        return redirect('organization_list')

    # الحصول على جميع الأعضاء مع معلومات الدورات
    members = organization.members.filter(is_active=True).prefetch_related('cycle_participations')

    # إحصائيات
    total_members = members.count()
    current_round = organization.get_current_cycle_round()

    context = {
        'organization': organization,
        'members': members,
        'total_members': total_members,
        'current_round': current_round,
        'cycle_stats': get_cycle_statistics(organization),
    }

    return render(request, 'core/cycle_members_list.html', context)


# ========== views صندوق العائلة ==========

@super_admin_required
def family_fund_create_view(request):
    """إنشاء صندوق عائلة جديد"""
    if request.method == 'POST':
        form = FamilyFundForm(request.POST)
        if form.is_valid():
            organization = form.save()

            # إنشاء اشتراك مجاني للصندوق
            Subscription.objects.create(
                organization=organization,
                subscription_type='free',
                status='active'
            )

            # تسجيل النشاط
            log_activity(
                user=request.user,
                action_type='create',
                model_name='Organization',
                object_id=organization.id,
                description=f'إنشاء صندوق عائلة جديد: {organization.name}',
                ip_address=get_client_ip(request)
            )

            messages.success(request, f'تم إنشاء صندوق العائلة "{organization.name}" بنجاح')
            return redirect('organization_list')
    else:
        form = FamilyFundForm()

    return render(request, 'core/family_fund_form.html', {
        'form': form,
        'title': 'إنشاء صندوق عائلة جديد'
    })


@manager_required
def family_fund_dashboard_view(request):
    """لوحة تحكم صندوق العائلة"""
    user = request.user

    if user.user_type == 'super_admin':
        # مالك المشروع يمكنه رؤية جميع صناديق العائلة
        organizations = Organization.objects.filter(organization_type='family_fund', is_active=True)
        organization = None
    else:
        # المدير يرى صندوقه فقط
        organization = user.organization
        if not organization or not organization.is_family_fund():
            messages.error(request, 'حسابك غير مرتبط بصندوق عائلة')
            return redirect('dashboard')
        organizations = [organization]

    context = {
        'organizations': organizations,
        'organization': organization,
        'is_family_fund_view': True,
    }

    # إضافة إحصائيات للصندوق المحدد
    if organization:
        # إحصائيات القروض
        total_loans = organization.family_loans.count()
        active_loans = organization.family_loans.filter(status='active').count()
        pending_loans = organization.family_loans.filter(status='pending').count()
        total_loan_amount = organization.family_loans.filter(
            status__in=['active', 'completed']
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

        # إحصائيات السدادات
        total_repaid = LoanRepayment.objects.filter(
            loan__organization=organization
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

        # الرصيد الحالي
        current_balance = organization.get_total_balance()

        context['fund_stats'] = {
            'total_loans': total_loans,
            'active_loans': active_loans,
            'pending_loans': pending_loans,
            'total_loan_amount': total_loan_amount,
            'total_repaid': total_repaid,
            'current_balance': current_balance,
            'emergency_fund_target': organization.emergency_fund_target,
            'monthly_contribution': organization.monthly_contribution,
        }

    return render(request, 'core/family_fund_dashboard.html', context)


@manager_required
def family_loan_create_view(request, org_id):
    """إنشاء طلب قرض جديد"""
    organization = get_object_or_404(Organization, id=org_id)

    # التحقق من الصلاحيات
    if request.user.user_type == 'manager' and request.user.organization != organization:
        messages.error(request, 'ليس لديك صلاحية للوصول لهذا الصندوق')
        return redirect('family_fund_dashboard')

    if not organization.is_family_fund():
        messages.error(request, 'هذا ليس صندوق عائلة')
        return redirect('organization_list')

    if request.method == 'POST':
        form = FamilyLoanForm(request.POST, organization=organization)
        if form.is_valid():
            loan = form.save(commit=False)
            loan.organization = organization
            loan.save()

            # تسجيل النشاط
            log_activity(
                user=request.user,
                action_type='create',
                model_name='FamilyLoan',
                object_id=loan.id,
                description=f'إنشاء طلب قرض جديد للعضو {loan.member.name} - المبلغ: {loan.amount}',
                organization=organization,
                ip_address=get_client_ip(request)
            )

            messages.success(request, 'تم إنشاء طلب القرض بنجاح')
            return redirect('family_loan_detail', org_id=org_id, loan_id=loan.id)
    else:
        form = FamilyLoanForm(organization=organization)

    return render(request, 'core/family_loan_form.html', {
        'form': form,
        'organization': organization,
        'title': 'طلب قرض جديد'
    })


@manager_required
def family_loan_list_view(request, org_id):
    """قائمة القروض"""
    organization = get_object_or_404(Organization, id=org_id)

    # التحقق من الصلاحيات
    if request.user.user_type == 'manager' and request.user.organization != organization:
        messages.error(request, 'ليس لديك صلاحية للوصول لهذا الصندوق')
        return redirect('family_fund_dashboard')

    # الحصول على القروض مع الفلترة
    loans = organization.family_loans.select_related('member', 'approved_by').order_by('-request_date')

    # فلترة حسب الحالة
    status = request.GET.get('status')
    if status:
        loans = loans.filter(status=status)

    # فلترة حسب العضو
    member_id = request.GET.get('member')
    if member_id:
        loans = loans.filter(member_id=member_id)

    context = {
        'organization': organization,
        'loans': loans,
        'members': organization.members.filter(is_active=True),
        'loan_statuses': FamilyLoan.LOAN_STATUS_CHOICES,
        'current_status': status,
        'current_member': member_id,
    }

    return render(request, 'core/family_loan_list.html', context)