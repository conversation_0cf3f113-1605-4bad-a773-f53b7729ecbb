# إعدادات العملة لنظام إدارة التضامن

# العملة الافتراضية
DEFAULT_CURRENCY = {
    'code': 'ILS',  # Israeli New Shekel
    'symbol': '₪',
    'name': 'شيقل',
    'name_en': 'Shekel',
    'decimal_places': 2,
    'locale': 'ar-PS',  # Arabic (Palestine)
}

# العملات المدعومة
SUPPORTED_CURRENCIES = {
    'ILS': {
        'code': 'ILS',
        'symbol': '₪',
        'name': 'شيقل',
        'name_en': 'Israeli New Shekel',
        'decimal_places': 2,
        'locale': 'ar-PS',
    },
    'USD': {
        'code': 'USD',
        'symbol': '$',
        'name': 'دولار أمريكي',
        'name_en': 'US Dollar',
        'decimal_places': 2,
        'locale': 'en-US',
    },
    'EUR': {
        'code': 'EUR',
        'symbol': '€',
        'name': 'يورو',
        'name_en': 'Euro',
        'decimal_places': 2,
        'locale': 'en-EU',
    },
    'SAR': {
        'code': 'SAR',
        'symbol': 'ر.س',
        'name': 'ريال سعودي',
        'name_en': 'Saudi Riyal',
        'decimal_places': 2,
        'locale': 'ar-SA',
    },
    'JOD': {
        'code': 'JOD',
        'symbol': 'د.أ',
        'name': 'دينار أردني',
        'name_en': 'Jordanian Dinar',
        'decimal_places': 3,
        'locale': 'ar-JO',
    },
}

def get_currency_symbol(currency_code=None):
    """الحصول على رمز العملة"""
    if currency_code is None:
        currency_code = DEFAULT_CURRENCY['code']
    
    return SUPPORTED_CURRENCIES.get(currency_code, DEFAULT_CURRENCY)['symbol']

def get_currency_name(currency_code=None):
    """الحصول على اسم العملة"""
    if currency_code is None:
        currency_code = DEFAULT_CURRENCY['code']
    
    return SUPPORTED_CURRENCIES.get(currency_code, DEFAULT_CURRENCY)['name']

def format_currency(amount, currency_code=None):
    """تنسيق المبلغ مع العملة"""
    if currency_code is None:
        currency_code = DEFAULT_CURRENCY['code']
    
    currency = SUPPORTED_CURRENCIES.get(currency_code, DEFAULT_CURRENCY)
    
    # تنسيق المبلغ
    formatted_amount = f"{float(amount):,.{currency['decimal_places']}f}"
    
    # إضافة رمز العملة
    return f"{formatted_amount} {currency['name']}"

def get_currency_locale(currency_code=None):
    """الحصول على locale للعملة"""
    if currency_code is None:
        currency_code = DEFAULT_CURRENCY['code']
    
    return SUPPORTED_CURRENCIES.get(currency_code, DEFAULT_CURRENCY)['locale']
