{% extends 'base.html' %}
{% load static %}
{% load currency_tags %}

{% block title %}لوحة التحكم - نظام إدارة التضامن{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="page-title">
            <i class="fas fa-tachometer-alt me-3"></i>
            لوحة التحكم
        </h1>
        <p class="text-muted mb-4">
            مرحباً {{ user.get_full_name|default:user.username }}، 
            {% if user.user_type == 'super_admin' %}
                مالك المشروع
            {% else %}
                مدير {{ user.organization.name }}
            {% endif %}
        </p>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    {% if user.user_type == 'super_admin' %}
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon">
                <i class="fas fa-building"></i>
            </div>
            <div class="stats-number" id="organizations-count">{{ stats.organizations_count|english_number:0 }}</div>
            <div class="stats-label">المؤسسات</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon">
                <i class="fas fa-user-tie"></i>
            </div>
            <div class="stats-number" id="managers-count">{{ stats.managers_count|english_number:0 }}</div>
            <div class="stats-label">المدراء</div>
        </div>
    </div>
    {% endif %}
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stats-number" id="members-count">{{ stats.members_count|english_number:0 }}</div>
            <div class="stats-label">الأعضاء</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="stats-number" id="total-payments">{{ stats.total_payments|english_currency }}</div>
            <div class="stats-label">إجمالي الدفعات</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon">
                <i class="fas fa-receipt"></i>
            </div>
            <div class="stats-number" id="total-expenses">{{ stats.total_expenses|english_currency }}</div>
            <div class="stats-label">إجمالي المصاريف</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon">
                <i class="fas fa-wallet"></i>
            </div>
            <div class="stats-number" id="balance">{{ stats.balance|english_currency }}</div>
            <div class="stats-label">الرصيد الحالي</div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الرسوم البيانية -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    الدفعات الشهرية
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="monthly-payments-chart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    أنواع الدفعات
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="payment-types-chart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الأعضاء المتأخرين -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    الأعضاء المتأخرين
                </h5>
                <a href="{% url 'member_list' %}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if late_members %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>آخر دفعة</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for member in late_members %}
                            <tr>
                                <td>{{ member.name }}</td>
                                <td>
                                    {% if member.last_payment %}
                                        {{ member.last_payment.payment_date }}
                                    {% else %}
                                        لا توجد دفعات
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="member-status status-danger">
                                        متأخر
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                    <p class="text-muted">جميع الأعضاء منتظمون في الدفع</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- آخر الأنشطة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    آخر الأنشطة
                </h5>
                <a href="#" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if recent_activities %}
                <div class="list-group list-group-flush">
                    {% for activity in recent_activities %}
                    <div class="list-group-item border-0 px-0">
                        <div class="d-flex align-items-center">
                            <div class="icon-circle icon-primary me-3">
                                <i class="fas fa-{{ activity.icon }}"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ activity.description }}</h6>
                                <small class="text-muted">
                                    {{ activity.user.get_full_name|default:activity.user.username }} - 
                                    {{ activity.created_at|timesince }} مضت
                                </small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-inbox text-muted fa-3x mb-3"></i>
                    <p class="text-muted">لا توجد أنشطة حديثة</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- روابط سريعة -->
<div class="row">
    <div class="col-12">
        <div class="alert alert-info mb-3">
            لإدارة الأعضاء بشكل كامل (عرض التفاصيل، تعديل، حذف)، يرجى الذهاب إلى <a href="{% url 'member_list' %}">قائمة الأعضاء</a>.
        </div>
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if user.user_type == 'super_admin' %}
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'organization_create' %}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-plus me-2"></i>
                            إضافة مؤسسة
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'manager_create' %}" class="btn btn-outline-success w-100">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة مدير
                        </a>
                    </div>
                    {% endif %}
                    
                    {% if user.user_type in 'manager,super_admin' %}
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'member_create' %}" class="btn btn-outline-info w-100">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة عضو
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'payment_create' %}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-money-bill me-2"></i>
                            تسجيل دفعة
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'expense_create' %}" class="btn btn-outline-danger w-100">
                            <i class="fas fa-receipt me-2"></i>
                            تسجيل مصروف
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'monthly_report' %}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-chart-bar me-2"></i>
                            التقرير الشهري
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحديث البيانات كل دقيقة
    setInterval(function() {
        updateStats();
    }, 60000);
</script>
{% endblock %}
