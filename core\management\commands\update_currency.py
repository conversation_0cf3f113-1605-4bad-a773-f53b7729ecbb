from django.core.management.base import BaseCommand
from core.currency_settings import SUPPORTED_CURRENCIES, DEFAULT_CURRENCY


class Command(BaseCommand):
    help = 'عرض وتحديث إعدادات العملة في النظام'

    def add_arguments(self, parser):
        parser.add_argument(
            '--show',
            action='store_true',
            help='عرض العملات المدعومة'
        )
        parser.add_argument(
            '--set-default',
            type=str,
            help='تعيين العملة الافتراضية (مثل: ILS, USD, EUR)'
        )

    def handle(self, *args, **options):
        if options['show']:
            self.show_currencies()
        elif options['set_default']:
            self.set_default_currency(options['set_default'])
        else:
            self.show_current_settings()

    def show_currencies(self):
        """عرض جميع العملات المدعومة"""
        self.stdout.write('='*60)
        self.stdout.write('العملات المدعومة في النظام:')
        self.stdout.write('='*60)
        
        for code, info in SUPPORTED_CURRENCIES.items():
            is_default = '✓ (افتراضية)' if code == DEFAULT_CURRENCY['code'] else ''
            self.stdout.write(f'{code}: {info["name"]} ({info["symbol"]}) {is_default}')
        
        self.stdout.write('='*60)

    def show_current_settings(self):
        """عرض الإعدادات الحالية"""
        self.stdout.write('='*60)
        self.stdout.write('إعدادات العملة الحالية:')
        self.stdout.write('='*60)
        
        current = DEFAULT_CURRENCY
        self.stdout.write(f'العملة الافتراضية: {current["name"]} ({current["code"]})')
        self.stdout.write(f'الرمز: {current["symbol"]}')
        self.stdout.write(f'المنطقة: {current["locale"]}')
        self.stdout.write(f'الخانات العشرية: {current["decimal_places"]}')
        
        self.stdout.write('\nالعملات المدعومة:')
        for code, info in SUPPORTED_CURRENCIES.items():
            status = '✓ نشطة' if code == current['code'] else '  متاحة'
            self.stdout.write(f'  {status} {code}: {info["name"]} ({info["symbol"]})')
        
        self.stdout.write('='*60)
        self.stdout.write('لتغيير العملة الافتراضية، استخدم:')
        self.stdout.write('python manage.py update_currency --set-default ILS')
        self.stdout.write('='*60)

    def set_default_currency(self, currency_code):
        """تعيين العملة الافتراضية"""
        currency_code = currency_code.upper()
        
        if currency_code not in SUPPORTED_CURRENCIES:
            self.stdout.write(
                self.style.ERROR(f'العملة {currency_code} غير مدعومة')
            )
            self.stdout.write('العملات المدعومة:')
            for code in SUPPORTED_CURRENCIES.keys():
                self.stdout.write(f'  - {code}')
            return
        
        # هنا يمكن إضافة كود لتحديث قاعدة البيانات أو ملف الإعدادات
        # حالياً سنعرض رسالة تأكيد فقط
        
        currency_info = SUPPORTED_CURRENCIES[currency_code]
        self.stdout.write(
            self.style.SUCCESS(
                f'تم تعيين {currency_info["name"]} ({currency_code}) كعملة افتراضية'
            )
        )
        
        self.stdout.write('\nملاحظة: لتطبيق التغيير بشكل كامل:')
        self.stdout.write('1. قم بتحديث ملف core/currency_settings.py')
        self.stdout.write('2. أعد تشغيل الخادم')
        self.stdout.write('3. امسح ذاكرة التخزين المؤقت للمتصفح')

    def format_example(self, amount=1000, currency_code='ILS'):
        """عرض مثال على تنسيق العملة"""
        from core.currency_settings import format_currency
        
        try:
            formatted = format_currency(amount, currency_code)
            return formatted
        except:
            return f'{amount} {SUPPORTED_CURRENCIES[currency_code]["name"]}'
