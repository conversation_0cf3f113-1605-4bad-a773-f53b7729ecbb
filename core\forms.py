from django import forms
from django.contrib.auth.forms import AuthenticationForm, UserCreationForm
from django.contrib.auth import authenticate
from .models import User, Organization, Member, Payment, Expense, CycleRound, CycleMember, FamilyLoan, LoanRepayment
from decimal import Decimal


class LoginForm(AuthenticationForm):
    """نموذج تسجيل الدخول"""
    username = forms.CharField(
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'اسم المستخدم أو البريد الإلكتروني',
            'dir': 'ltr'
        }),
        label='اسم المستخدم'
    )
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'كلمة المرور',
            'dir': 'ltr'
        }),
        label='كلمة المرور'
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['username'].widget.attrs.update({'autofocus': True})


class OrganizationForm(forms.ModelForm):
    """نموذج المؤسسة العادية"""
    class Meta:
        model = Organization
        fields = ['name', 'description', 'address', 'phone', 'email']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم المؤسسة'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'وصف المؤسسة'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'العنوان'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الهاتف',
                'dir': 'ltr'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'البريد الإلكتروني',
                'dir': 'ltr'
            }),
        }

    def save(self, commit=True):
        organization = super().save(commit=False)
        organization.organization_type = 'regular'
        if commit:
            organization.save()
        return organization


class ManagerForm(forms.ModelForm):
    """نموذج إنشاء مدير جديد"""
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'كلمة المرور'
        }),
        label='كلمة المرور'
    )
    confirm_password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'تأكيد كلمة المرور'
        }),
        label='تأكيد كلمة المرور'
    )

    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'username', 'email', 'phone', 'organization']
        widgets = {
            'first_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الاسم الأول'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم العائلة'
            }),
            'username': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم المستخدم',
                'dir': 'ltr'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'البريد الإلكتروني',
                'dir': 'ltr'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الهاتف',
                'dir': 'ltr'
            }),
            'organization': forms.Select(attrs={
                'class': 'form-control'
            }),
        }

    def clean(self):
        cleaned_data = super().clean()
        password = cleaned_data.get('password')
        confirm_password = cleaned_data.get('confirm_password')

        if password and confirm_password and password != confirm_password:
            raise forms.ValidationError('كلمات المرور غير متطابقة')

        return cleaned_data

    def save(self, commit=True):
        user = super().save(commit=False)
        user.user_type = 'manager'
        user.set_password(self.cleaned_data['password'])
        if commit:
            user.save()
        return user


class MemberForm(forms.ModelForm):
    """نموذج العضو"""
    class Meta:
        model = Member
        fields = ['name', 'phone', 'city', 'notes']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم العضو'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الهاتف',
                'dir': 'ltr'
            }),
            'city': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'المدينة'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات'
            }),
        }

    def __init__(self, *args, **kwargs):
        self.organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)

    def clean_phone(self):
        phone = self.cleaned_data.get('phone')
        if phone and self.organization:
            # التحقق من عدم تكرار رقم الهاتف في نفس المؤسسة
            existing = Member.objects.filter(
                organization=self.organization,
                phone=phone
            )
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)

            if existing.exists():
                raise forms.ValidationError('رقم الهاتف مسجل مسبقاً في هذه المؤسسة')

        return phone

    def clean(self):
        cleaned_data = super().clean()

        # التحقق من الحد الأقصى لعدد الأعضاء (فقط عند إضافة عضو جديد)
        if self.organization and not self.instance.pk:
            if not self.organization.can_add_member():
                raise forms.ValidationError(
                    'لقد وصلت الحد الاقصى لعدد الاعضاء الرجاء التواصل مع مالك المشروع على الرقم 0598455262'
                )

        return cleaned_data


class PaymentForm(forms.ModelForm):
    """نموذج الدفعة"""
    class Meta:
        model = Payment
        fields = ['member', 'amount', 'payment_type', 'payment_method', 'payment_date', 'notes']
        widgets = {
            'member': forms.Select(attrs={
                'class': 'form-control'
            }),
            'amount': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'المبلغ',
                'step': '0.01',
                'min': '0.01'
            }),
            'payment_type': forms.Select(attrs={
                'class': 'form-control'
            }),
            'payment_method': forms.Select(attrs={
                'class': 'form-control'
            }),
            'payment_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات'
            }),
        }

    def __init__(self, *args, **kwargs):
        self.organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)
        
        if self.organization:
            self.fields['member'].queryset = Member.objects.filter(
                organization=self.organization,
                is_active=True
            )

    def clean_amount(self):
        amount = self.cleaned_data.get('amount')
        if amount and amount <= 0:
            raise forms.ValidationError('المبلغ يجب أن يكون أكبر من صفر')
        return amount


class ExpenseForm(forms.ModelForm):
    """نموذج المصروف"""
    class Meta:
        model = Expense
        fields = ['expense_type', 'amount', 'expense_date', 'beneficiary', 'description', 'notes']
        widgets = {
            'expense_type': forms.Select(attrs={
                'class': 'form-control'
            }),
            'amount': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'المبلغ',
                'step': '0.01',
                'min': '0.01'
            }),
            'expense_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'beneficiary': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'المستفيد'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'وصف المصروف'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'ملاحظات'
            }),
        }

    def clean_amount(self):
        amount = self.cleaned_data.get('amount')
        if amount and amount <= 0:
            raise forms.ValidationError('المبلغ يجب أن يكون أكبر من صفر')
        return amount


class ReportFilterForm(forms.Form):
    """نموذج فلترة التقارير"""
    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='من تاريخ'
    )
    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='إلى تاريخ'
    )
    member = forms.ModelChoiceField(
        queryset=Member.objects.none(),
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        label='العضو'
    )
    payment_type = forms.ChoiceField(
        choices=[('', 'جميع الأنواع')] + list(Payment.PAYMENT_TYPES),
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        label='نوع الدفعة'
    )

    def __init__(self, *args, **kwargs):
        organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)
        
        if organization:
            self.fields['member'].queryset = Member.objects.filter(
                organization=organization,
                is_active=True
            )


class CyclicOrganizationForm(forms.ModelForm):
    """نموذج الجمعية الدورية"""
    class Meta:
        model = Organization
        fields = [
            'name', 'description', 'phone', 'email',
            'monthly_amount', 'cycle_duration_days', 'cycle_start_date'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم الجمعية الدورية'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'وصف الجمعية'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الهاتف',
                'dir': 'ltr'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'البريد الإلكتروني',
                'dir': 'ltr'
            }),
            'monthly_amount': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'المبلغ الشهري',
                'step': '0.01',
                'min': '1'
            }),
            'cycle_duration_days': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'مدة الدورة بالأيام',
                'value': '30',
                'min': '1'
            }),
            'cycle_start_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
        }

    def save(self, commit=True):
        organization = super().save(commit=False)
        organization.organization_type = 'cyclic'
        if commit:
            organization.save()
        return organization


class CycleRoundForm(forms.ModelForm):
    """نموذج دورة الجمعية"""
    class Meta:
        model = CycleRound
        fields = ['start_date', 'end_date', 'notes']
        widgets = {
            'start_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'end_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات'
            }),
        }


class CycleMemberForm(forms.ModelForm):
    """نموذج عضو الدورة"""
    class Meta:
        model = CycleMember
        fields = ['payment_status', 'paid_amount', 'payment_date', 'notes']
        widgets = {
            'payment_status': forms.Select(attrs={
                'class': 'form-control'
            }),
            'paid_amount': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'المبلغ المدفوع',
                'step': '0.01',
                'min': '0'
            }),
            'payment_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'ملاحظات'
            }),
        }


class FamilyFundForm(forms.ModelForm):
    """نموذج صندوق العائلة"""
    class Meta:
        model = Organization
        fields = [
            'name', 'family_name', 'description', 'phone', 'email',
            'emergency_fund_target', 'monthly_contribution', 'allow_loans', 'max_loan_amount'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم صندوق العائلة'
            }),
            'family_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم العائلة'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'وصف الصندوق'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الهاتف',
                'dir': 'ltr'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'البريد الإلكتروني',
                'dir': 'ltr'
            }),
            'emergency_fund_target': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'هدف صندوق الطوارئ',
                'step': '0.01',
                'min': '0'
            }),
            'monthly_contribution': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'المساهمة الشهرية المطلوبة',
                'step': '0.01',
                'min': '0'
            }),
            'allow_loans': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'max_loan_amount': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'الحد الأقصى للقرض',
                'step': '0.01',
                'min': '0'
            }),
        }

    def save(self, commit=True):
        organization = super().save(commit=False)
        organization.organization_type = 'family_fund'
        if commit:
            organization.save()
        return organization


class FamilyLoanForm(forms.ModelForm):
    """نموذج طلب قرض عائلي"""
    class Meta:
        model = FamilyLoan
        fields = ['member', 'loan_type', 'amount', 'purpose', 'due_date']
        widgets = {
            'member': forms.Select(attrs={
                'class': 'form-control'
            }),
            'loan_type': forms.Select(attrs={
                'class': 'form-control'
            }),
            'amount': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'مبلغ القرض',
                'step': '0.01',
                'min': '1'
            }),
            'purpose': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'الغرض من القرض'
            }),
            'due_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
        }

    def __init__(self, *args, **kwargs):
        self.organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)

        if self.organization:
            self.fields['member'].queryset = Member.objects.filter(
                organization=self.organization,
                is_active=True
            )

    def clean_amount(self):
        amount = self.cleaned_data.get('amount')
        if amount and self.organization and self.organization.max_loan_amount:
            if amount > self.organization.max_loan_amount:
                raise forms.ValidationError(
                    f'المبلغ يتجاوز الحد الأقصى المسموح ({self.organization.max_loan_amount} شيقل)'
                )
        return amount


class LoanRepaymentForm(forms.ModelForm):
    """نموذج سداد القرض"""
    class Meta:
        model = LoanRepayment
        fields = ['amount', 'payment_date', 'payment_method', 'notes']
        widgets = {
            'amount': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'مبلغ السداد',
                'step': '0.01',
                'min': '0.01'
            }),
            'payment_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'payment_method': forms.Select(attrs={
                'class': 'form-control'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'ملاحظات'
            }),
        }
