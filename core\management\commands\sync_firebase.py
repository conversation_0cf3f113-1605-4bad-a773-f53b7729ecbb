from django.core.management.base import BaseCommand
from core.models import User, Organization, Member, Payment, Expense, ActivityLog
from core.firebase_config import firebase_service
from datetime import datetime
import json


class Command(BaseCommand):
    help = 'مزامنة البيانات مع Firebase Firestore'

    def add_arguments(self, parser):
        parser.add_argument(
            '--sync-all',
            action='store_true',
            help='مزامنة جميع البيانات'
        )
        parser.add_argument(
            '--sync-users',
            action='store_true',
            help='مزامنة المستخدمين فقط'
        )
        parser.add_argument(
            '--sync-organizations',
            action='store_true',
            help='مزامنة المؤسسات فقط'
        )
        parser.add_argument(
            '--sync-members',
            action='store_true',
            help='مزامنة الأعضاء فقط'
        )
        parser.add_argument(
            '--sync-payments',
            action='store_true',
            help='مزامنة الدفعات فقط'
        )
        parser.add_argument(
            '--sync-expenses',
            action='store_true',
            help='مزامنة المصاريف فقط'
        )

    def handle(self, *args, **options):
        self.stdout.write('بدء مزامنة البيانات مع Firebase...')
        
        try:
            if options['sync_all'] or options['sync_users']:
                self.sync_users()
            
            if options['sync_all'] or options['sync_organizations']:
                self.sync_organizations()
            
            if options['sync_all'] or options['sync_members']:
                self.sync_members()
            
            if options['sync_all'] or options['sync_payments']:
                self.sync_payments()
            
            if options['sync_all'] or options['sync_expenses']:
                self.sync_expenses()
            
            if not any([options['sync_all'], options['sync_users'], 
                       options['sync_organizations'], options['sync_members'],
                       options['sync_payments'], options['sync_expenses']]):
                self.stdout.write(
                    self.style.WARNING(
                        'لم يتم تحديد نوع البيانات للمزامنة. استخدم --sync-all أو خيار محدد.'
                    )
                )
                return
            
            self.stdout.write(
                self.style.SUCCESS('تمت مزامنة البيانات مع Firebase بنجاح!')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'خطأ في مزامنة البيانات: {str(e)}')
            )

    def sync_users(self):
        """مزامنة المستخدمين"""
        self.stdout.write('مزامنة المستخدمين...')
        users = User.objects.all()
        
        for user in users:
            user_data = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'user_type': user.user_type,
                'organization_id': user.organization.id if user.organization else None,
                'organization_name': user.organization.name if user.organization else None,
                'phone': user.phone,
                'is_active': user.is_active,
                'is_active_user': user.is_active_user,
                'created_at': user.date_joined.isoformat(),
                'updated_at': user.updated_at.isoformat() if hasattr(user, 'updated_at') else None,
            }
            
            # إضافة المستخدم إلى Firebase
            doc_id = firebase_service.add_document('users', user_data)
            if doc_id:
                self.stdout.write(f'  ✓ تم مزامنة المستخدم: {user.username}')
            else:
                self.stdout.write(f'  ✗ فشل في مزامنة المستخدم: {user.username}')

    def sync_organizations(self):
        """مزامنة المؤسسات"""
        self.stdout.write('مزامنة المؤسسات...')
        organizations = Organization.objects.all()
        
        for org in organizations:
            org_data = {
                'id': org.id,
                'name': org.name,
                'description': org.description,
                'address': org.address,
                'phone': org.phone,
                'email': org.email,
                'is_active': org.is_active,
                'total_members': org.get_total_members(),
                'total_balance': float(org.get_total_balance()),
                'created_at': org.created_at.isoformat(),
                'updated_at': org.updated_at.isoformat(),
            }
            
            doc_id = firebase_service.add_document('organizations', org_data)
            if doc_id:
                self.stdout.write(f'  ✓ تم مزامنة المؤسسة: {org.name}')
            else:
                self.stdout.write(f'  ✗ فشل في مزامنة المؤسسة: {org.name}')

    def sync_members(self):
        """مزامنة الأعضاء"""
        self.stdout.write('مزامنة الأعضاء...')
        members = Member.objects.all()
        
        for member in members:
            member_data = {
                'id': member.id,
                'organization_id': member.organization.id,
                'organization_name': member.organization.name,
                'name': member.name,
                'phone': member.phone,
                'city': member.city,
                'notes': member.notes,
                'is_active': member.is_active,
                'total_payments': float(member.get_total_payments()),
                'last_payment_date': member.get_last_payment().payment_date.isoformat() if member.get_last_payment() else None,
                'created_at': member.created_at.isoformat(),
                'updated_at': member.updated_at.isoformat(),
            }
            
            doc_id = firebase_service.add_document('members', member_data)
            if doc_id:
                self.stdout.write(f'  ✓ تم مزامنة العضو: {member.name}')
            else:
                self.stdout.write(f'  ✗ فشل في مزامنة العضو: {member.name}')

    def sync_payments(self):
        """مزامنة الدفعات"""
        self.stdout.write('مزامنة الدفعات...')
        payments = Payment.objects.all()
        
        for payment in payments:
            payment_data = {
                'id': payment.id,
                'organization_id': payment.organization.id,
                'organization_name': payment.organization.name,
                'member_id': payment.member.id,
                'member_name': payment.member.name,
                'amount': float(payment.amount),
                'payment_type': payment.payment_type,
                'payment_method': payment.payment_method,
                'payment_date': payment.payment_date.isoformat(),
                'notes': payment.notes,
                'created_by_id': payment.created_by.id,
                'created_by_name': payment.created_by.get_full_name() or payment.created_by.username,
                'created_at': payment.created_at.isoformat(),
                'updated_at': payment.updated_at.isoformat(),
            }
            
            doc_id = firebase_service.add_document('payments', payment_data)
            if doc_id:
                self.stdout.write(f'  ✓ تم مزامنة الدفعة: {payment.id}')
            else:
                self.stdout.write(f'  ✗ فشل في مزامنة الدفعة: {payment.id}')

    def sync_expenses(self):
        """مزامنة المصاريف"""
        self.stdout.write('مزامنة المصاريف...')
        expenses = Expense.objects.all()
        
        for expense in expenses:
            expense_data = {
                'id': expense.id,
                'organization_id': expense.organization.id,
                'organization_name': expense.organization.name,
                'expense_type': expense.expense_type,
                'amount': float(expense.amount),
                'expense_date': expense.expense_date.isoformat(),
                'beneficiary': expense.beneficiary,
                'description': expense.description,
                'notes': expense.notes,
                'created_by_id': expense.created_by.id,
                'created_by_name': expense.created_by.get_full_name() or expense.created_by.username,
                'created_at': expense.created_at.isoformat(),
                'updated_at': expense.updated_at.isoformat(),
            }
            
            doc_id = firebase_service.add_document('expenses', expense_data)
            if doc_id:
                self.stdout.write(f'  ✓ تم مزامنة المصروف: {expense.id}')
            else:
                self.stdout.write(f'  ✗ فشل في مزامنة المصروف: {expense.id}')
