from django.core.management.base import BaseCommand
from core.models import User


class Command(BaseCommand):
    help = 'إعادة تعيين كلمات المرور للمستخدمين'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            help='اسم المستخدم المحدد لإعادة تعيين كلمة المرور'
        )
        parser.add_argument(
            '--password',
            type=str,
            help='كلمة المرور الجديدة'
        )
        parser.add_argument(
            '--all-managers',
            action='store_true',
            help='إعادة تعيين كلمات المرور لجميع المدراء'
        )

    def handle(self, *args, **options):
        self.stdout.write('بدء إعادة تعيين كلمات المرور...')
        
        if options['username'] and options['password']:
            # إعادة تعيين كلمة مرور مستخدم محدد
            try:
                user = User.objects.get(username=options['username'])
                user.set_password(options['password'])
                user.save()
                self.stdout.write(
                    self.style.SUCCESS(
                        f'تم تغيير كلمة مرور المستخدم {user.username} بنجاح'
                    )
                )
            except User.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'المستخدم {options["username"]} غير موجود')
                )
        
        elif options['all_managers']:
            # إعادة تعيين كلمات المرور لجميع المدراء
            managers = User.objects.filter(user_type='manager')
            for manager in managers:
                manager.set_password('manager123')
                manager.save()
                self.stdout.write(f'تم تغيير كلمة مرور المدير {manager.username}')
            
            self.stdout.write(
                self.style.SUCCESS(f'تم تغيير كلمات مرور {managers.count()} مدير')
            )
        
        else:
            # إعادة تعيين كلمات المرور الافتراضية
            self.stdout.write('إعادة تعيين كلمات المرور الافتراضية...')
            
            # مالك المشروع
            try:
                admin_user = User.objects.get(username='admin')
                admin_user.set_password('admin123')
                admin_user.save()
                self.stdout.write('✓ تم تغيير كلمة مرور المدير العام (admin)')
            except User.DoesNotExist:
                self.stdout.write('⚠ المدير العام (admin) غير موجود')
            
            # المدراء
            manager_passwords = {
                'manager1': 'manager123',
                'manager2': 'manager123',
                'manager3': 'manager123'
            }
            
            for username, password in manager_passwords.items():
                try:
                    user = User.objects.get(username=username)
                    user.set_password(password)
                    user.save()
                    self.stdout.write(f'✓ تم تغيير كلمة مرور {username}')
                except User.DoesNotExist:
                    self.stdout.write(f'⚠ المستخدم {username} غير موجود')
            
            self.stdout.write(
                self.style.SUCCESS('تم إعادة تعيين جميع كلمات المرور الافتراضية')
            )
        
        # عرض معلومات تسجيل الدخول
        self.stdout.write('\n' + '='*50)
        self.stdout.write('معلومات تسجيل الدخول:')
        self.stdout.write('='*50)
        
        # مالك المشروع
        try:
            admin_user = User.objects.get(username='admin')
            self.stdout.write(f'مالك المشروع:')
            self.stdout.write(f'  اسم المستخدم: admin')
            self.stdout.write(f'  كلمة المرور: admin123')
            self.stdout.write(f'  البريد الإلكتروني: {admin_user.email}')
            self.stdout.write(f'  الحالة: {"نشط" if admin_user.is_active else "غير نشط"}')
        except User.DoesNotExist:
            pass
        
        # المدراء
        managers = User.objects.filter(user_type='manager')
        if managers.exists():
            self.stdout.write(f'\nالمدراء ({managers.count()}):')
            for manager in managers:
                self.stdout.write(f'  {manager.username}: manager123 ({manager.organization.name if manager.organization else "بدون مؤسسة"})')
        
        self.stdout.write('='*50)
        self.stdout.write('رابط النظام: http://127.0.0.1:8000')
        self.stdout.write('رابط معلومات تسجيل الدخول: http://127.0.0.1:8000/login-help/')
        self.stdout.write('='*50)
