from django.core.management.base import BaseCommand
from core.firebase_config import firebase_service
from datetime import datetime


class Command(BaseCommand):
    help = 'اختبار اتصال Firebase'

    def handle(self, *args, **options):
        self.stdout.write('اختبار اتصال Firebase...')
        
        try:
            # اختبار إضافة مستند
            test_data = {
                'test': True,
                'message': 'اختبار اتصال Firebase',
                'timestamp': datetime.now().isoformat(),
                'created_by': 'Django Management Command'
            }
            
            self.stdout.write('محاولة إضافة مستند اختبار...')
            doc_id = firebase_service.add_document('test_connection', test_data)
            
            if doc_id:
                self.stdout.write(
                    self.style.SUCCESS(f'✓ تم إنشاء مستند اختبار بنجاح! ID: {doc_id}')
                )
                
                # اختبار قراءة المستند
                self.stdout.write('محاولة قراءة المستند...')
                retrieved_data = firebase_service.get_document('test_connection', doc_id)
                
                if retrieved_data:
                    self.stdout.write(
                        self.style.SUCCESS('✓ تم قراءة المستند بنجاح!')
                    )
                    self.stdout.write(f'البيانات المسترجعة: {retrieved_data}')
                    
                    # اختبار تحديث المستند
                    self.stdout.write('محاولة تحديث المستند...')
                    update_data = {
                        'updated': True,
                        'update_timestamp': datetime.now().isoformat()
                    }
                    
                    if firebase_service.update_document('test_connection', doc_id, update_data):
                        self.stdout.write(
                            self.style.SUCCESS('✓ تم تحديث المستند بنجاح!')
                        )
                        
                        # اختبار حذف المستند
                        self.stdout.write('محاولة حذف المستند...')
                        if firebase_service.delete_document('test_connection', doc_id):
                            self.stdout.write(
                                self.style.SUCCESS('✓ تم حذف المستند بنجاح!')
                            )
                        else:
                            self.stdout.write(
                                self.style.ERROR('✗ فشل في حذف المستند')
                            )
                    else:
                        self.stdout.write(
                            self.style.ERROR('✗ فشل في تحديث المستند')
                        )
                else:
                    self.stdout.write(
                        self.style.ERROR('✗ فشل في قراءة المستند')
                    )
                    
            else:
                self.stdout.write(
                    self.style.ERROR('✗ فشل في إنشاء مستند اختبار')
                )
            
            # اختبار قراءة مجموعة
            self.stdout.write('اختبار قراءة مجموعة...')
            collection_data = firebase_service.get_collection('test_connection', limit=5)
            
            self.stdout.write(
                self.style.SUCCESS(f'✓ تم العثور على {len(collection_data)} مستندات في المجموعة')
            )
            
            # معلومات المشروع
            self.stdout.write('\n' + '='*50)
            self.stdout.write('معلومات مشروع Firebase:')
            self.stdout.write(f'Project ID: tadamon-4a9bb')
            self.stdout.write(f'Service Account: <EMAIL>')
            self.stdout.write('='*50)
            
            self.stdout.write(
                self.style.SUCCESS('\n🎉 Firebase يعمل بشكل صحيح!')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ خطأ في اتصال Firebase: {str(e)}')
            )
            self.stdout.write('\nتأكد من:')
            self.stdout.write('1. صحة معلومات Firebase في ملف .env')
            self.stdout.write('2. تفعيل Firestore في مشروع Firebase')
            self.stdout.write('3. صحة صلاحيات Service Account')
            self.stdout.write('4. اتصال الإنترنت')

    def add_arguments(self, parser):
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='عرض تفاصيل أكثر'
        )
