from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from core.models import Organization, Member, Payment, Expense, Subscription
from decimal import Decimal
from datetime import datetime, timedelta
import random

User = get_user_model()


class Command(BaseCommand):
    help = 'إنشاء بيانات تجريبية للنظام'

    def add_arguments(self, parser):
        parser.add_argument(
            '--organizations',
            type=int,
            default=3,
            help='عدد المؤسسات المراد إنشاؤها'
        )
        parser.add_argument(
            '--members-per-org',
            type=int,
            default=10,
            help='عدد الأعضاء لكل مؤسسة'
        )

    def handle(self, *args, **options):
        self.stdout.write('بدء إنشاء البيانات التجريبية...')
        
        # إنشاء مستخدم مالك المشروع إذا لم يكن موجوداً
        super_admin, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'مدير',
                'last_name': 'النظام',
                'user_type': 'super_admin',
                'is_staff': True,
                'is_superuser': True,
            }
        )
        if created:
            super_admin.set_password('admin123')
            super_admin.save()
            self.stdout.write(f'تم إنشاء مستخدم مالك المشروع: {super_admin.username}')

        # إنشاء المؤسسات
        organizations_data = [
            {
                'name': 'جمعية الخير الخيرية',
                'description': 'جمعية خيرية تهدف لمساعدة المحتاجين',
                'address': 'الرياض، حي النخيل',
                'phone': '0112345678',
                'email': '<EMAIL>'
            },
            {
                'name': 'صندوق التكافل الاجتماعي',
                'description': 'صندوق لدعم الأسر المحتاجة',
                'address': 'جدة، حي الصفا',
                'phone': '0126789012',
                'email': '<EMAIL>'
            },
            {
                'name': 'جمعية البر والإحسان',
                'description': 'جمعية تعمل في مجال البر والإحسان',
                'address': 'الدمام، حي الفيصلية',
                'phone': '0138901234',
                'email': '<EMAIL>'
            }
        ]

        organizations = []
        for i in range(min(options['organizations'], len(organizations_data))):
            org_data = organizations_data[i]
            org, created = Organization.objects.get_or_create(
                name=org_data['name'],
                defaults=org_data
            )
            if created:
                # إنشاء اشتراك للمؤسسة
                Subscription.objects.create(
                    organization=org,
                    subscription_type='free',
                    status='active'
                )
                self.stdout.write(f'تم إنشاء المؤسسة: {org.name}')
            organizations.append(org)

        # إنشاء مدراء للمؤسسات
        managers_data = [
            {
                'username': 'manager1',
                'email': '<EMAIL>',
                'first_name': 'أحمد',
                'last_name': 'المدير',
                'phone': '0501234567'
            },
            {
                'username': 'manager2',
                'email': '<EMAIL>',
                'first_name': 'محمد',
                'last_name': 'الإداري',
                'phone': '0507654321'
            },
            {
                'username': 'manager3',
                'email': '<EMAIL>',
                'first_name': 'عبدالله',
                'last_name': 'المسؤول',
                'phone': '0509876543'
            }
        ]

        for i, org in enumerate(organizations):
            if i < len(managers_data):
                manager_data = managers_data[i]
                manager, created = User.objects.get_or_create(
                    username=manager_data['username'],
                    defaults={
                        **manager_data,
                        'user_type': 'manager',
                        'organization': org,
                        'is_staff': False,
                        'is_superuser': False,
                    }
                )
                if created:
                    manager.set_password('manager123')
                    manager.save()
                    self.stdout.write(f'تم إنشاء المدير: {manager.get_full_name()} للمؤسسة: {org.name}')

        # إنشاء الأعضاء
        members_names = [
            'عبدالرحمن أحمد', 'فاطمة محمد', 'خالد عبدالله', 'عائشة سالم',
            'يوسف إبراهيم', 'مريم عبدالعزيز', 'عمر حسن', 'زينب علي',
            'سعد محمود', 'نورا أحمد', 'طارق سعيد', 'هند عبدالله',
            'ماجد فهد', 'سارة محمد', 'بدر عبدالرحمن', 'ليلى حسام'
        ]

        cities = ['الرياض', 'جدة', 'الدمام', 'مكة', 'المدينة', 'الطائف', 'أبها', 'تبوك']

        for org in organizations:
            for i in range(options['members_per_org']):
                if i < len(members_names):
                    name = members_names[i]
                else:
                    name = f'عضو {i+1}'
                
                member, created = Member.objects.get_or_create(
                    organization=org,
                    name=name,
                    defaults={
                        'phone': f'05{random.randint(10000000, 99999999)}',
                        'city': random.choice(cities),
                        'notes': f'عضو في {org.name}',
                        'is_active': True
                    }
                )
                if created:
                    # إنشاء دفعات للعضو
                    for month in range(1, 7):  # آخر 6 أشهر
                        payment_date = datetime.now().date() - timedelta(days=30*month)
                        Payment.objects.create(
                            organization=org,
                            member=member,
                            amount=Decimal(str(random.randint(100, 500))),
                            payment_type=random.choice(['monthly', 'annual', 'donation']),
                            payment_method=random.choice(['cash', 'bank_transfer', 'online']),
                            payment_date=payment_date,
                            notes=f'دفعة شهر {payment_date.month}',
                            created_by=super_admin
                        )

        # إنشاء مصاريف
        expense_types = ['operational', 'maintenance', 'utilities', 'salaries', 'other']
        expense_descriptions = [
            'مصاريف إدارية', 'صيانة المكاتب', 'فواتير الكهرباء',
            'رواتب الموظفين', 'مصاريف متنوعة'
        ]

        for org in organizations:
            for i in range(5):  # 5 مصاريف لكل مؤسسة
                expense_date = datetime.now().date() - timedelta(days=random.randint(1, 180))
                Expense.objects.create(
                    organization=org,
                    expense_type=random.choice(expense_types),
                    amount=Decimal(str(random.randint(200, 1000))),
                    expense_date=expense_date,
                    beneficiary=f'مستفيد {i+1}',
                    description=random.choice(expense_descriptions),
                    notes=f'مصروف للمؤسسة {org.name}',
                    created_by=super_admin
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'تم إنشاء البيانات التجريبية بنجاح:\n'
                f'- {len(organizations)} مؤسسات\n'
                f'- {Member.objects.count()} أعضاء\n'
                f'- {Payment.objects.count()} دفعات\n'
                f'- {Expense.objects.count()} مصاريف'
            )
        )
