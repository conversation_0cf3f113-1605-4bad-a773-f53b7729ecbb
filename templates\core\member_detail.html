{% extends 'base.html' %}
{% load static %}
{% load currency_tags %}

{% block title %}{{ title }} - نظام إدارة التضامن{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="page-title">
                <i class="fas fa-user me-3"></i>
                {{ title }}
            </h1>
            <div>
                <a href="{% url 'member_edit' member.id %}" class="btn btn-warning me-2">
                    <i class="fas fa-edit me-2"></i>
                    تعديل المعلومات
                </a>
                <a href="{% url 'member_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- معلومات العضو الأساسية -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    المعلومات الأساسية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الاسم الكامل</label>
                        <p class="fw-bold fs-5">{{ member.name }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">رقم الهاتف</label>
                        <p class="fw-bold">
                            <a href="tel:{{ member.phone }}" class="text-decoration-none">
                                <i class="fas fa-phone me-2"></i>{{ member.phone }}
                            </a>
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">المدينة</label>
                        <p class="fw-bold">{{ member.city }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">حالة العضوية</label>
                        <p>
                            {% if member.is_active %}
                                <span class="badge bg-success fs-6">نشط</span>
                            {% else %}
                                <span class="badge bg-danger fs-6">غير نشط</span>
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">تاريخ التسجيل</label>
                        <p class="fw-bold">{{ member.created_at|date:"Y/m/d H:i" }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">آخر تحديث</label>
                        <p class="fw-bold">{{ member.updated_at|date:"Y/m/d H:i" }}</p>
                    </div>
                    {% if member.notes %}
                    <div class="col-12">
                        <label class="form-label text-muted">الملاحظات</label>
                        <div class="alert alert-info">
                            <i class="fas fa-sticky-note me-2"></i>
                            {{ member.notes }}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- إحصائيات العضو -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات العضو
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <h3 class="text-success">{{ total_payments|english_currency:0 }}</h3>
                    <small class="text-muted">إجمالي الدفعات</small>
                </div>
                
                <hr>
                
                <div class="mb-3">
                    <label class="form-label text-muted">آخر دفعة</label>
                    {% if last_payment %}
                        <p class="fw-bold">{{ last_payment.payment_date|date:"Y/m/d" }}</p>
                        <small class="text-muted">{{ last_payment.amount|english_currency:0 }}</small>
                    {% else %}
                        <p class="text-muted">لا توجد دفعات</p>
                    {% endif %}
                </div>
                
                <div class="mb-3">
                    <label class="form-label text-muted">حالة الدفع</label>
                    <p>
                        <span class="badge bg-{{ payment_status.status_class }} fs-6">
                            {{ payment_status.status }}
                        </span>
                    </p>
                    {% if payment_status.days_since_payment %}
                        <small class="text-muted">
                            {{ payment_status.days_since_payment }} يوم منذ آخر دفعة
                        </small>
                    {% endif %}
                </div>
                
                <div class="mb-3">
                    <label class="form-label text-muted">عدد الدفعات</label>
                    <p class="fw-bold">{{ payments.count }} دفعة</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- سجل الدفعات -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    سجل الدفعات ({{ payments.count }})
                </h5>
                <a href="{% url 'payment_create' %}?member_id={{ member.id }}" class="btn btn-success btn-sm">
                    <i class="fas fa-plus me-2"></i>
                    إضافة دفعة جديدة
                </a>
            </div>
            <div class="card-body">
                {% if payments %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>نوع الدفعة</th>
                                <th>الملاحظات</th>
                                <th>تاريخ الإدخال</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in payments %}
                            <tr>
                                <td>{{ payment.payment_date|date:"Y/m/d" }}</td>
                                <td>
                                    <span class="fw-bold text-success">
                                        {{ payment.amount|english_currency:0 }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ payment.payment_type }}</span>
                                </td>
                                <td>
                                    {% if payment.notes %}
                                        {{ payment.notes|truncatechars:50 }}
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">{{ payment.created_at|date:"Y/m/d H:i" }}</small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد دفعات مسجلة</h4>
                    <p class="text-muted">لم يتم تسجيل أي دفعات لهذا العضو بعد</p>
                    <a href="{% url 'payment_create' %}?member_id={{ member.id }}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>
                        إضافة أول دفعة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إجراءات سريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-tools me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'payment_create' %}?member_id={{ member.id }}" class="btn btn-success w-100">
                            <i class="fas fa-money-bill-wave d-block mb-2 fa-2x"></i>
                            إضافة دفعة
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'member_edit' member.id %}" class="btn btn-warning w-100">
                            <i class="fas fa-edit d-block mb-2 fa-2x"></i>
                            تعديل المعلومات
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="tel:{{ member.phone }}" class="btn btn-info w-100">
                            <i class="fas fa-phone d-block mb-2 fa-2x"></i>
                            اتصال مباشر
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button type="button" class="btn btn-danger w-100 btn-delete-member" 
                                data-member-id="{{ member.id }}"
                                data-member-name="{{ member.name }}">
                            <i class="fas fa-trash d-block mb-2 fa-2x"></i>
                            حذف العضو
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // حذف العضو عبر AJAX
        document.querySelectorAll('.btn-delete-member').forEach(function(btn) {
            btn.addEventListener('click', function() {
                var memberId = this.getAttribute('data-member-id');
                var memberName = this.getAttribute('data-member-name');
                if (confirm('هل أنت متأكد من حذف العضو "' + memberName + '"؟\n\nسيتم حذف جميع دفعات العضو أيضاً!')) {
                    fetch('{% url 'member_delete' 0 %}'.replace('0', memberId), {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': '{{ csrf_token }}',
                            'X-Requested-With': 'XMLHttpRequest',
                        },
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.href = '{% url 'member_list' %}';
                        } else {
                            alert(data.error || 'حدث خطأ أثناء الحذف');
                        }
                    });
                }
            });
        });
    });
</script>
{% endblock %}
