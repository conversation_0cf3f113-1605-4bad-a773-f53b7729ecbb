{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام إدارة التضامن{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="page-title">
                <i class="fas fa-money-bill me-3"></i>
                {{ title }}
            </h1>
            <a href="{% url 'payment_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i>
                    معلومات الدفعة
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="paymentForm" data-validate>
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.member.id_for_label }}" class="form-label required">
                                <i class="fas fa-user me-2"></i>
                                {{ form.member.label }}
                            </label>
                            {{ form.member }}
                            {% if form.member.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.member.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.amount.id_for_label }}" class="form-label required">
                                <i class="fas fa-money-bill me-2"></i>
                                {{ form.amount.label }}
                            </label>
                            <div class="input-group">
                                {{ form.amount }}
                                <span class="input-group-text">شيقل</span>
                            </div>
                            {% if form.amount.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.amount.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.payment_type.id_for_label }}" class="form-label required">
                                <i class="fas fa-tag me-2"></i>
                                {{ form.payment_type.label }}
                            </label>
                            {{ form.payment_type }}
                            {% if form.payment_type.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.payment_type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.payment_method.id_for_label }}" class="form-label required">
                                <i class="fas fa-credit-card me-2"></i>
                                {{ form.payment_method.label }}
                            </label>
                            {{ form.payment_method }}
                            {% if form.payment_method.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.payment_method.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.payment_date.id_for_label }}" class="form-label required">
                                <i class="fas fa-calendar me-2"></i>
                                {{ form.payment_date.label }}
                            </label>
                            {{ form.payment_date }}
                            {% if form.payment_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.payment_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                <i class="fas fa-sticky-note me-2"></i>
                                {{ form.notes.label }}
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.notes.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    
                    <hr class="section-divider">
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'payment_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ الدفعة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row justify-content-center mt-4">
    <div class="col-lg-8">
        <div class="card bg-light">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مهمة
                </h6>
                <ul class="mb-0">
                    <li>جميع الحقول المطلوبة يجب تعبئتها</li>
                    <li>المبلغ يجب أن يكون أكبر من صفر</li>
                    <li>تاريخ الدفع يمكن أن يكون في الماضي أو الحاضر</li>
                    <li>يمكن إضافة ملاحظات إضافية عن الدفعة</li>
                    <li>سيتم تسجيل الدفعة باسم المستخدم الحالي</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تعيين التاريخ الحالي كافتراضي
        const dateField = document.getElementById('{{ form.payment_date.id_for_label }}');
        if (dateField && !dateField.value) {
            const today = new Date().toISOString().split('T')[0];
            dateField.value = today;
        }
        
        // تركيز على حقل العضو
        const memberField = document.getElementById('{{ form.member.id_for_label }}');
        if (memberField) {
            memberField.focus();
        }
        
        // تنسيق حقل المبلغ
        const amountField = document.getElementById('{{ form.amount.id_for_label }}');
        if (amountField) {
            amountField.addEventListener('input', function() {
                // إزالة الأحرف غير الرقمية والنقطة العشرية
                this.value = this.value.replace(/[^0-9.]/g, '');
                
                // التأكد من وجود نقطة عشرية واحدة فقط
                const parts = this.value.split('.');
                if (parts.length > 2) {
                    this.value = parts[0] + '.' + parts.slice(1).join('');
                }
            });
        }
        
        // تأثيرات بصرية للنموذج
        const form = document.getElementById('paymentForm');
        form.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
            submitBtn.disabled = true;
        });
    });
</script>
{% endblock %}
