{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام إدارة التضامن{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="page-title">
                <i class="fas fa-user-tie me-3"></i>
                {{ title }}
            </h1>
            <a href="{% url 'organization_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-plus me-2"></i>
                    معلومات المدير
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="managerForm" data-validate>
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.first_name.id_for_label }}" class="form-label required">
                                <i class="fas fa-user me-2"></i>
                                {{ form.first_name.label }}
                            </label>
                            {{ form.first_name }}
                            {% if form.first_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.first_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.last_name.id_for_label }}" class="form-label required">
                                <i class="fas fa-user me-2"></i>
                                {{ form.last_name.label }}
                            </label>
                            {{ form.last_name }}
                            {% if form.last_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.last_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label required">
                                <i class="fas fa-at me-2"></i>
                                {{ form.username.label }}
                            </label>
                            {{ form.username }}
                            {% if form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label required">
                                <i class="fas fa-envelope me-2"></i>
                                {{ form.email.label }}
                            </label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.phone.id_for_label }}" class="form-label">
                                <i class="fas fa-phone me-2"></i>
                                {{ form.phone.label }}
                            </label>
                            {{ form.phone }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.phone.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.organization.id_for_label }}" class="form-label required">
                                <i class="fas fa-building me-2"></i>
                                {{ form.organization.label }}
                            </label>
                            {{ form.organization }}
                            {% if form.organization.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.organization.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <hr class="section-divider">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.password.id_for_label }}" class="form-label required">
                                <i class="fas fa-lock me-2"></i>
                                {{ form.password.label }}
                            </label>
                            {{ form.password }}
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.confirm_password.id_for_label }}" class="form-label required">
                                <i class="fas fa-lock me-2"></i>
                                {{ form.confirm_password.label }}
                            </label>
                            {{ form.confirm_password }}
                            {% if form.confirm_password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.confirm_password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    
                    <hr class="section-divider">
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'organization_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ المدير
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row justify-content-center mt-4">
    <div class="col-lg-8">
        <div class="card bg-light">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مهمة
                </h6>
                <ul class="mb-0">
                    <li>جميع الحقول المطلوبة يجب تعبئتها</li>
                    <li>اسم المستخدم يجب أن يكون فريداً في النظام</li>
                    <li>كلمة المرور يجب أن تكون قوية وآمنة</li>
                    <li>المدير سيتمكن من إدارة المؤسسة المحددة فقط</li>
                    <li>يمكن تعديل معلومات المدير لاحقاً</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تركيز على حقل الاسم الأول
        const firstNameField = document.getElementById('{{ form.first_name.id_for_label }}');
        if (firstNameField) {
            firstNameField.focus();
        }
        
        // التحقق من تطابق كلمات المرور
        const passwordField = document.getElementById('{{ form.password.id_for_label }}');
        const confirmPasswordField = document.getElementById('{{ form.confirm_password.id_for_label }}');
        
        function checkPasswordMatch() {
            if (passwordField.value && confirmPasswordField.value) {
                if (passwordField.value !== confirmPasswordField.value) {
                    confirmPasswordField.setCustomValidity('كلمات المرور غير متطابقة');
                    confirmPasswordField.classList.add('is-invalid');
                } else {
                    confirmPasswordField.setCustomValidity('');
                    confirmPasswordField.classList.remove('is-invalid');
                }
            }
        }
        
        if (passwordField && confirmPasswordField) {
            passwordField.addEventListener('input', checkPasswordMatch);
            confirmPasswordField.addEventListener('input', checkPasswordMatch);
        }
        
        // تأثيرات بصرية للنموذج
        const form = document.getElementById('managerForm');
        form.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
            submitBtn.disabled = true;
        });
    });
</script>
{% endblock %}
