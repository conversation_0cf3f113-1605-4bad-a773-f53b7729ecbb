# دليل إعداد Firebase لنظام إدارة التضامن

## 🔥 نظرة عامة

تم تكوين النظام للعمل مع Firebase Firestore كقاعدة بيانات سحابية. هذا الدليل يوضح كيفية إعداد Firebase والاستفادة من المميزات المتاحة.

## 📋 المتطلبات

- حساب Google
- مشروع Firebase
- Python 3.11+
- Django 5.1.2
- firebase-admin

## 🚀 خطوات الإعداد

### 1. إنشاء مشروع Firebase

1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. انقر على "إضافة مشروع" أو "Add project"
3. أدخل اسم المشروع (مثل: tadamon-system)
4. اخ<PERSON><PERSON> إعدادات Google Analytics (اختياري)
5. انقر على "إنشاء المشروع"

### 2. تفعيل Firestore

1. في لوحة تحكم Firebase، اذهب إلى "Firestore Database"
2. انقر على "إنشاء قاعدة بيانات"
3. اختر "Start in test mode" للبداية
4. اختر موقع قاعدة البيانات (يفضل أقرب منطقة)

### 3. إنشاء Service Account

1. اذهب إلى "Project Settings" (إعدادات المشروع)
2. انقر على تبويب "Service accounts"
3. انقر على "Generate new private key"
4. احفظ ملف JSON الذي تم تنزيله

### 4. تكوين النظام

1. انسخ محتويات ملف JSON إلى ملف `.env`:

```env
# Firebase Configuration
FIREBASE_TYPE=service_account
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=your-client-cert-url
```

## 🧪 اختبار الإعداد

### اختبار الاتصال
```bash
python manage.py test_firebase
```

### مزامنة البيانات
```bash
# مزامنة جميع البيانات
python manage.py sync_firebase --sync-all

# مزامنة نوع محدد من البيانات
python manage.py sync_firebase --sync-users
python manage.py sync_firebase --sync-organizations
python manage.py sync_firebase --sync-members
python manage.py sync_firebase --sync-payments
python manage.py sync_firebase --sync-expenses
```

## 📊 هيكل البيانات في Firestore

### Collections (المجموعات)

#### `users/`
```json
{
  "id": 1,
  "username": "admin",
  "email": "<EMAIL>",
  "user_type": "super_admin",
  "organization_id": null,
  "is_active": true,
  "created_at": "2025-01-01T00:00:00Z"
}
```

#### `organizations/`
```json
{
  "id": 1,
  "name": "جمعية الخير الخيرية",
  "description": "جمعية خيرية تهدف لمساعدة المحتاجين",
  "phone": "0112345678",
  "email": "<EMAIL>",
  "is_active": true,
  "total_members": 25,
  "total_balance": 15000.50,
  "created_at": "2025-01-01T00:00:00Z"
}
```

#### `members/`
```json
{
  "id": 1,
  "organization_id": 1,
  "organization_name": "جمعية الخير الخيرية",
  "name": "أحمد محمد",
  "phone": "0501234567",
  "city": "الرياض",
  "is_active": true,
  "total_payments": 1200.00,
  "last_payment_date": "2025-01-01",
  "created_at": "2025-01-01T00:00:00Z"
}
```

#### `payments/`
```json
{
  "id": 1,
  "organization_id": 1,
  "member_id": 1,
  "member_name": "أحمد محمد",
  "amount": 200.00,
  "payment_type": "monthly",
  "payment_method": "cash",
  "payment_date": "2025-01-01",
  "created_by_name": "مدير النظام",
  "created_at": "2025-01-01T00:00:00Z"
}
```

#### `expenses/`
```json
{
  "id": 1,
  "organization_id": 1,
  "expense_type": "operational",
  "amount": 500.00,
  "expense_date": "2025-01-01",
  "beneficiary": "شركة الصيانة",
  "description": "صيانة المكاتب",
  "created_by_name": "مدير النظام",
  "created_at": "2025-01-01T00:00:00Z"
}
```

## 🔐 قواعد الأمان

### قواعد Firestore الموصى بها

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد للمستخدمين
    match /users/{userId} {
      allow read, write: if request.auth != null;
    }
    
    // قواعد للمؤسسات
    match /organizations/{orgId} {
      allow read, write: if request.auth != null;
    }
    
    // قواعد للأعضاء
    match /members/{memberId} {
      allow read, write: if request.auth != null;
    }
    
    // قواعد للدفعات
    match /payments/{paymentId} {
      allow read, write: if request.auth != null;
    }
    
    // قواعد للمصاريف
    match /expenses/{expenseId} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## 🛠️ الأوامر المتاحة

### أوامر Django المخصصة

```bash
# اختبار اتصال Firebase
python manage.py test_firebase

# مزامنة جميع البيانات
python manage.py sync_firebase --sync-all

# مزامنة المستخدمين فقط
python manage.py sync_firebase --sync-users

# مزامنة المؤسسات فقط
python manage.py sync_firebase --sync-organizations

# مزامنة الأعضاء فقط
python manage.py sync_firebase --sync-members

# مزامنة الدفعات فقط
python manage.py sync_firebase --sync-payments

# مزامنة المصاريف فقط
python manage.py sync_firebase --sync-expenses
```

## 📱 لوحة تحكم Firebase

يمكنك الوصول للوحة تحكم Firebase من خلال:
- الرابط: `/firebase-dashboard/`
- القائمة: التقارير > Firebase Dashboard

### المميزات المتاحة:
- عرض إحصائيات البيانات من Firebase
- عرض آخر الدفعات والمصاريف
- روابط سريعة لـ Firebase Console
- أدوات المزامنة والاختبار

## 🔄 المزامنة التلقائية

يمكن إعداد مزامنة تلقائية للبيانات من خلال:

### 1. Cron Jobs (Linux/Mac)
```bash
# إضافة إلى crontab
0 */6 * * * cd /path/to/project && python manage.py sync_firebase --sync-all
```

### 2. Task Scheduler (Windows)
إنشاء مهمة مجدولة لتشغيل أمر المزامنة كل 6 ساعات

### 3. Django Celery (للمشاريع الكبيرة)
```python
# في tasks.py
from celery import shared_task
from django.core.management import call_command

@shared_task
def sync_firebase_data():
    call_command('sync_firebase', '--sync-all')
```

## 🚨 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في المصادقة
```
Error: Invalid credentials
```
**الحل**: تأكد من صحة معلومات Service Account في ملف `.env`

#### 2. خطأ في الصلاحيات
```
Error: Permission denied
```
**الحل**: تأكد من تفعيل Firestore وإعداد قواعد الأمان

#### 3. خطأ في الاتصال
```
Error: Connection timeout
```
**الحل**: تأكد من اتصال الإنترنت وإعدادات الشبكة

## 📞 الدعم

للحصول على المساعدة:
- راجع [وثائق Firebase](https://firebase.google.com/docs/firestore)
- تحقق من [Django Firebase Admin](https://firebase.google.com/docs/admin/setup)
- اتصل بفريق التطوير

---

**ملاحظة**: تأكد من عدم مشاركة معلومات Service Account مع أي شخص آخر للحفاظ على أمان البيانات.
