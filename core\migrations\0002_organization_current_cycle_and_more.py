# Generated by Django 5.1.2 on 2025-07-01 22:21

import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='organization',
            name='current_cycle',
            field=models.IntegerField(default=0, verbose_name='رقم الدورة الحالية'),
        ),
        migrations.AddField(
            model_name='organization',
            name='cycle_duration_days',
            field=models.IntegerField(default=30, verbose_name='مدة الدورة بالأيام'),
        ),
        migrations.AddField(
            model_name='organization',
            name='cycle_start_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ بداية الجمعية الدورية'),
        ),
        migrations.AddField(
            model_name='organization',
            name='is_cycle_active',
            field=models.BooleanField(default=False, verbose_name='هل الدورة نشطة'),
        ),
        migrations.AddField(
            model_name='organization',
            name='monthly_amount',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='المبلغ الشهري'),
        ),
        migrations.AddField(
            model_name='organization',
            name='organization_type',
            field=models.CharField(choices=[('regular', 'جمعية عادية'), ('cyclic', 'جمعية دورية نسائية')], default='regular', max_length=20, verbose_name='نوع الجمعية'),
        ),
        migrations.CreateModel(
            name='CycleRound',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('round_number', models.IntegerField(verbose_name='رقم الدورة')),
                ('start_date', models.DateField(verbose_name='تاريخ بداية الدورة')),
                ('end_date', models.DateField(verbose_name='تاريخ نهاية الدورة')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('active', 'نشطة'), ('completed', 'مكتملة'), ('cancelled', 'ملغية')], default='pending', max_length=20, verbose_name='حالة الدورة')),
                ('total_amount', models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=10, verbose_name='إجمالي المبلغ')),
                ('draw_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ السحب')),
                ('is_amount_received', models.BooleanField(default=False, verbose_name='تم استلام المبلغ')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cycle_rounds', to='core.organization')),
                ('winner_member', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='won_rounds', to='core.member', verbose_name='العضو الفائز')),
            ],
            options={
                'verbose_name': 'دورة',
                'verbose_name_plural': 'الدورات',
                'ordering': ['-round_number'],
                'unique_together': {('organization', 'round_number')},
            },
        ),
        migrations.CreateModel(
            name='CycleMember',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_status', models.CharField(choices=[('paid', 'دفع'), ('not_paid', 'لم يدفع'), ('partial', 'دفع جزئي')], default='not_paid', max_length=20, verbose_name='حالة الدفع')),
                ('member_status', models.CharField(choices=[('active', 'نشط'), ('received', 'استلم'), ('excluded', 'مستبعد')], default='active', max_length=20, verbose_name='حالة العضو')),
                ('paid_amount', models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=10, verbose_name='المبلغ المدفوع')),
                ('payment_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الدفع')),
                ('is_eligible_for_draw', models.BooleanField(default=True, verbose_name='مؤهل للسحب')),
                ('has_received_before', models.BooleanField(default=False, verbose_name='استلم من قبل')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cycle_participations', to='core.member')),
                ('cycle_round', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cycle_members', to='core.cycleround')),
            ],
            options={
                'verbose_name': 'عضو دورة',
                'verbose_name_plural': 'أعضاء الدورات',
                'unique_together': {('cycle_round', 'member')},
            },
        ),
    ]
