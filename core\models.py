from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils import timezone
from decimal import Decimal


class User(AbstractUser):
    """نموذج المستخدم المخصص"""
    USER_TYPES = (
        ('super_admin', 'مالك المشروع'),
        ('manager', 'مدير صندوق/جمعية'),
    )

    user_type = models.CharField(max_length=20, choices=USER_TYPES, default='manager')
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE, null=True, blank=True)
    phone = models.CharField(max_length=20, blank=True)
    is_active_user = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.username} - {self.get_user_type_display()}"


class Organization(models.Model):
    """نموذج المؤسسات/الصناديق/الجمعيات"""
    name = models.CharField(max_length=200, verbose_name="اسم المؤسسة")
    description = models.TextField(blank=True, verbose_name="الوصف")
    address = models.TextField(blank=True, verbose_name="العنوان")
    phone = models.CharField(max_length=20, blank=True, verbose_name="الهاتف")
    email = models.EmailField(blank=True, verbose_name="البريد الإلكتروني")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "مؤسسة"
        verbose_name_plural = "المؤسسات"

    def __str__(self):
        return self.name

    def get_active_members_count(self):
        """الحصول على عدد الأعضاء النشطين"""
        return self.members.filter(is_active=True).count()

    def get_max_members_limit(self):
        """الحصول على الحد الأقصى لعدد الأعضاء"""
        try:
            return self.subscription.max_members
        except:
            return 50  # الحد الافتراضي

    def can_add_member(self):
        """التحقق من إمكانية إضافة عضو جديد"""
        return self.get_active_members_count() < self.get_max_members_limit()

    def get_remaining_member_slots(self):
        """الحصول على عدد الأماكن المتبقية للأعضاء"""
        return max(0, self.get_max_members_limit() - self.get_active_members_count())

    def get_total_members(self):
        return self.members.filter(is_active=True).count()

    def get_total_balance(self):
        total_payments = self.payments.aggregate(
            total=models.Sum('amount')
        )['total'] or Decimal('0')

        total_expenses = self.expenses.aggregate(
            total=models.Sum('amount')
        )['total'] or Decimal('0')

        return total_payments - total_expenses


class Member(models.Model):
    """نموذج الأعضاء"""
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='members')
    name = models.CharField(max_length=200, verbose_name="الاسم")
    phone = models.CharField(max_length=20, verbose_name="رقم الهاتف")
    city = models.CharField(max_length=100, verbose_name="المدينة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "عضو"
        verbose_name_plural = "الأعضاء"
        unique_together = ['organization', 'phone']

    def __str__(self):
        return f"{self.name} - {self.organization.name}"

    def get_total_payments(self):
        return self.payments.aggregate(
            total=models.Sum('amount')
        )['total'] or Decimal('0')

    def get_last_payment(self):
        return self.payments.order_by('-payment_date').first()

    def get_payment_status(self):
        """الحصول على حالة دفعات العضو"""
        from .utils import get_member_payment_status
        return get_member_payment_status(self)


class Payment(models.Model):
    """نموذج الدفعات"""
    PAYMENT_TYPES = (
        ('monthly', 'اشتراك شهري'),
        ('annual', 'اشتراك سنوي'),
        ('donation', 'تبرع'),
        ('other', 'أخرى'),
    )

    PAYMENT_METHODS = (
        ('cash', 'نقدي'),
        ('bank_transfer', 'تحويل بنكي'),
        ('online', 'دفع إلكتروني'),
        ('check', 'شيك'),
    )

    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='payments')
    member = models.ForeignKey(Member, on_delete=models.CASCADE, related_name='payments')
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="المبلغ")
    payment_type = models.CharField(max_length=20, choices=PAYMENT_TYPES, verbose_name="نوع الدفعة")
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHODS, verbose_name="طريقة الدفع")
    payment_date = models.DateField(verbose_name="تاريخ الدفع")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "دفعة"
        verbose_name_plural = "الدفعات"
        ordering = ['-payment_date']

    def __str__(self):
        return f"{self.member.name} - {self.amount} - {self.payment_date}"


class Expense(models.Model):
    """نموذج المصاريف"""
    EXPENSE_TYPES = (
        ('operational', 'مصاريف تشغيلية'),
        ('maintenance', 'صيانة'),
        ('utilities', 'خدمات'),
        ('salaries', 'رواتب'),
        ('other', 'أخرى'),
    )

    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='expenses')
    expense_type = models.CharField(max_length=20, choices=EXPENSE_TYPES, verbose_name="نوع المصروف")
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="المبلغ")
    expense_date = models.DateField(verbose_name="تاريخ المصروف")
    beneficiary = models.CharField(max_length=200, verbose_name="المستفيد")
    description = models.TextField(verbose_name="الوصف")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "مصروف"
        verbose_name_plural = "المصاريف"
        ordering = ['-expense_date']

    def __str__(self):
        return f"{self.get_expense_type_display()} - {self.amount} - {self.expense_date}"


class Subscription(models.Model):
    """نموذج الاشتراكات"""
    SUBSCRIPTION_TYPES = (
        ('free', 'مجاني'),
        ('basic', 'أساسي'),
        ('premium', 'مميز'),
    )

    STATUS_CHOICES = (
        ('active', 'نشط'),
        ('expired', 'منتهي'),
        ('suspended', 'معلق'),
    )

    organization = models.OneToOneField(Organization, on_delete=models.CASCADE, related_name='subscription')
    subscription_type = models.CharField(max_length=20, choices=SUBSCRIPTION_TYPES, default='free')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    start_date = models.DateField(default=timezone.now)
    end_date = models.DateField(null=True, blank=True)
    max_members = models.IntegerField(default=50)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "اشتراك"
        verbose_name_plural = "الاشتراكات"

    def __str__(self):
        return f"{self.organization.name} - {self.get_subscription_type_display()}"

    def is_active(self):
        if self.status != 'active':
            return False
        if self.end_date and self.end_date < timezone.now().date():
            return False
        return True


class ActivityLog(models.Model):
    """نموذج سجل الأنشطة"""
    ACTION_TYPES = (
        ('create', 'إنشاء'),
        ('update', 'تحديث'),
        ('delete', 'حذف'),
        ('login', 'تسجيل دخول'),
        ('logout', 'تسجيل خروج'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, null=True, blank=True)
    action_type = models.CharField(max_length=20, choices=ACTION_TYPES)
    model_name = models.CharField(max_length=50, blank=True)
    object_id = models.CharField(max_length=50, blank=True)
    description = models.TextField()
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "سجل النشاط"
        verbose_name_plural = "سجلات الأنشطة"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.get_action_type_display()} - {self.created_at}"
