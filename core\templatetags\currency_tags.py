from django import template
from core.currency_settings import format_currency, get_currency_symbol, get_currency_name
import locale

register = template.Library()

@register.filter
def currency(value, currency_code=None):
    """
    تنسيق المبلغ مع العملة
    الاستخدام: {{ amount|currency }}
    أو: {{ amount|currency:"USD" }}
    """
    try:
        return format_currency(value, currency_code)
    except (ValueError, TypeError):
        return value

@register.filter
def currency_symbol(currency_code=None):
    """
    الحصول على رمز العملة
    الاستخدام: {{ "ILS"|currency_symbol }}
    """
    return get_currency_symbol(currency_code)

@register.filter
def currency_name(currency_code=None):
    """
    الحصول على اسم العملة
    الاستخدام: {{ "ILS"|currency_name }}
    """
    return get_currency_name(currency_code)

@register.simple_tag
def format_amount(amount, currency_code=None):
    """
    تنسيق المبلغ مع العملة كـ simple tag
    الاستخدام: {% format_amount amount %}
    أو: {% format_amount amount "USD" %}
    """
    try:
        return format_currency(amount, currency_code)
    except (ValueError, TypeError):
        return amount

@register.inclusion_tag('core/currency_display.html')
def currency_display(amount, currency_code=None, show_symbol=True, show_name=True):
    """
    عرض المبلغ مع العملة في قالب مخصص
    الاستخدام: {% currency_display amount %}
    """
    try:
        formatted_amount = format_currency(amount, currency_code)
        symbol = get_currency_symbol(currency_code) if show_symbol else ''
        name = get_currency_name(currency_code) if show_name else ''
        
        return {
            'amount': amount,
            'formatted_amount': formatted_amount,
            'symbol': symbol,
            'name': name,
            'currency_code': currency_code or 'ILS'
        }
    except (ValueError, TypeError):
        return {
            'amount': amount,
            'formatted_amount': str(amount),
            'symbol': '',
            'name': '',
            'currency_code': 'ILS'
        }


@register.filter
def english_number(value, decimal_places=1):
    """
    تنسيق الأرقام بالتنسيق الإنجليزي مع فاصلة الآلاف والفاصلة العشرية
    الاستخدام: {{ number|english_number }}
    أو: {{ number|english_number:2 }}
    """
    try:
        # تحويل القيمة إلى رقم
        if isinstance(value, str):
            # إزالة أي فواصل موجودة
            value = value.replace(',', '').replace('٬', '')

        num = float(value)

        # تنسيق الرقم مع فاصلة الآلاف والفاصلة العشرية
        if decimal_places == 0:
            # للأرقام الصحيحة
            formatted = f"{int(num):,}"
        else:
            # للأرقام العشرية
            formatted = f"{num:,.{decimal_places}f}"

        return formatted
    except (ValueError, TypeError, AttributeError):
        return value


@register.filter
def english_currency(value, decimal_places=1):
    """
    تنسيق المبلغ بالتنسيق الإنجليزي مع رمز العملة
    الاستخدام: {{ amount|english_currency }}
    أو: {{ amount|english_currency:2 }}
    """
    try:
        formatted_number = english_number(value, decimal_places)
        return f"{formatted_number} ₪"
    except:
        return value
