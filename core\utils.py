from django.contrib.auth.decorators import login_required
from django.core.exceptions import PermissionDenied
from django.shortcuts import redirect
from django.contrib import messages
from functools import wraps
from django.db.models import Sum
from .models import ActivityLog
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
from django.http import HttpResponse
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from datetime import datetime, timedelta
from django.utils import timezone
from decimal import Decimal


def super_admin_required(view_func):
    """ديكوريتر للتأكد من أن المستخدم مالك المشروع"""
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        if request.user.user_type != 'super_admin':
            messages.error(request, 'ليس لديك صلاحية للوصول لهذه الصفحة')
            return redirect('dashboard')
        return view_func(request, *args, **kwargs)
    return _wrapped_view


def manager_required(view_func):
    """ديكوريتر للتأكد من أن المستخدم مدير صندوق"""
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        if request.user.user_type not in ['manager', 'super_admin']:
            messages.error(request, 'ليس لديك صلاحية للوصول لهذه الصفحة')
            return redirect('dashboard')
        return view_func(request, *args, **kwargs)
    return _wrapped_view


def organization_access_required(view_func):
    """ديكوريتر للتأكد من أن المستخدم يمكنه الوصول للمؤسسة"""
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        if request.user.user_type == 'super_admin':
            return view_func(request, *args, **kwargs)
        
        # التحقق من أن المدير يصل لمؤسسته فقط
        org_id = kwargs.get('org_id') or request.GET.get('org_id')
        if org_id and str(request.user.organization.id) != str(org_id):
            messages.error(request, 'ليس لديك صلاحية للوصول لهذه المؤسسة')
            return redirect('dashboard')
        
        return view_func(request, *args, **kwargs)
    return _wrapped_view


def log_activity(user, action_type, description, organization=None, model_name=None, object_id=None, ip_address=None):
    """تسجيل نشاط المستخدم"""
    try:
        ActivityLog.objects.create(
            user=user,
            organization=organization,
            action_type=action_type,
            model_name=model_name,
            object_id=str(object_id) if object_id else '',
            description=description,
            ip_address=ip_address
        )
    except Exception as e:
        print(f"خطأ في تسجيل النشاط: {e}")


def get_client_ip(request):
    """الحصول على عنوان IP للعميل"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def export_to_excel(data, filename, sheet_name="البيانات"):
    """تصدير البيانات إلى Excel"""
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="{filename}.xlsx"'
    
    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    worksheet.title = sheet_name
    
    # تنسيق الرأس
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    if data:
        # كتابة الرؤوس
        headers = list(data[0].keys())
        for col, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
        
        # كتابة البيانات
        for row, item in enumerate(data, 2):
            for col, value in enumerate(item.values(), 1):
                worksheet.cell(row=row, column=col, value=value)
        
        # تعديل عرض الأعمدة
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    workbook.save(response)
    return response


def export_to_pdf(data, filename, title="تقرير"):
    """تصدير البيانات إلى PDF"""
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="{filename}.pdf"'
    
    doc = SimpleDocTemplate(response, pagesize=A4, rightMargin=72, leftMargin=72,
                           topMargin=72, bottomMargin=18)
    
    # إعداد الأنماط
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1,  # وسط
    )
    
    story = []
    
    # العنوان
    story.append(Paragraph(title, title_style))
    story.append(Spacer(1, 12))
    
    if data:
        # إنشاء الجدول
        table_data = []
        headers = list(data[0].keys())
        table_data.append(headers)
        
        for item in data:
            table_data.append(list(item.values()))
        
        table = Table(table_data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
    
    doc.build(story)
    return response


def get_monthly_stats(organization, year=None, month=None):
    """الحصول على إحصائيات شهرية"""
    if not year:
        year = timezone.now().year
    if not month:
        month = timezone.now().month
    
    # إحصائيات الدفعات
    payments = organization.payments.filter(
        payment_date__year=year,
        payment_date__month=month
    )
    
    # إحصائيات المصاريف
    expenses = organization.expenses.filter(
        expense_date__year=year,
        expense_date__month=month
    )
    
    total_payments = payments.aggregate(total=Sum('amount'))['total'] or Decimal('0')
    total_expenses = expenses.aggregate(total=Sum('amount'))['total'] or Decimal('0')
    
    return {
        'year': year,
        'month': month,
        'total_payments': total_payments,
        'total_expenses': total_expenses,
        'net_income': total_payments - total_expenses,
        'payments_count': payments.count(),
        'expenses_count': expenses.count(),
        'active_members': organization.get_total_members()
    }


def get_member_payment_status(member, months_back=3):
    """الحصول على حالة دفعات العضو"""
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=months_back * 30)
    
    payments = member.payments.filter(
        payment_date__gte=start_date,
        payment_date__lte=end_date
    ).order_by('-payment_date')
    
    last_payment = payments.first()
    total_amount = payments.aggregate(total=Sum('amount'))['total'] or Decimal('0')
    
    # تحديد حالة العضو
    if not last_payment:
        status = 'متأخر'
        status_class = 'danger'
        days_since_payment = 0
    else:
        days_since_payment = (end_date - last_payment.payment_date).days
        if days_since_payment > 45:
            status = 'متأخر'
            status_class = 'danger'
        elif days_since_payment > 30:
            status = 'تحذير'
            status_class = 'warning'
        else:
            status = 'منتظم'
            status_class = 'success'

    return {
        'status': status,
        'status_class': status_class,
        'days_since_payment': days_since_payment,
        'last_payment': last_payment,
        'total_amount': total_amount,
        'payments_count': payments.count()
    }
