from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from decimal import Decimal
from datetime import date, timedelta

from .models import Organization, Member, Payment, Subscription
from .utils import get_member_payment_status

User = get_user_model()


class MemberViewsTestCase(TestCase):
    """اختبارات عرض الأعضاء"""

    def setUp(self):
        """إعداد البيانات للاختبار"""
        # إنشاء مؤسسة
        self.organization = Organization.objects.create(
            name="مؤسسة اختبار",
            description="مؤسسة للاختبار"
        )

        # إنشاء اشتراك
        self.subscription = Subscription.objects.create(
            organization=self.organization,
            max_members=50
        )

        # إنشاء مدير
        self.manager = User.objects.create_user(
            username="manager_test",
            password="testpass123",
            user_type="manager",
            organization=self.organization
        )

        # إنشاء عضو
        self.member = Member.objects.create(
            organization=self.organization,
            name="عضو اختبار",
            phone="0599123456",
            city="غزة"
        )

        self.client = Client()

    def test_member_list_view(self):
        """اختبار عرض قائمة الأعضاء"""
        self.client.login(username="manager_test", password="testpass123")
        response = self.client.get(reverse('member_list'))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "عضو اختبار")
        self.assertContains(response, "قائمة الأعضاء")

    def test_member_detail_view(self):
        """اختبار عرض تفاصيل العضو"""
        self.client.login(username="manager_test", password="testpass123")
        response = self.client.get(reverse('member_detail', args=[self.member.id]))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.member.name)
        self.assertContains(response, "تفاصيل العضو")

    def test_member_edit_view(self):
        """اختبار تعديل العضو"""
        self.client.login(username="manager_test", password="testpass123")
        response = self.client.get(reverse('member_edit', args=[self.member.id]))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "تعديل العضو")

        # اختبار تحديث البيانات
        response = self.client.post(reverse('member_edit', args=[self.member.id]), {
            'name': 'عضو محدث',
            'phone': '0599123456',
            'city': 'رفح',
            'notes': 'ملاحظة جديدة'
        })

        self.assertEqual(response.status_code, 302)  # إعادة توجيه بعد النجاح

        # التحقق من التحديث
        self.member.refresh_from_db()
        self.assertEqual(self.member.name, 'عضو محدث')
        self.assertEqual(self.member.city, 'رفح')

    def test_member_payment_status(self):
        """اختبار حالة دفعات العضو"""
        # إنشاء دفعة حديثة
        Payment.objects.create(
            organization=self.organization,
            member=self.member,
            amount=Decimal('100.00'),
            payment_type='monthly',
            payment_method='cash',
            payment_date=date.today(),
            created_by=self.manager
        )

        status = get_member_payment_status(self.member)
        self.assertEqual(status['status'], 'منتظم')
        self.assertEqual(status['status_class'], 'success')

        # إنشاء دفعة قديمة
        old_payment_date = date.today() - timedelta(days=50)
        Payment.objects.filter(member=self.member).delete()
        Payment.objects.create(
            organization=self.organization,
            member=self.member,
            amount=Decimal('100.00'),
            payment_type='monthly',
            payment_method='cash',
            payment_date=old_payment_date,
            created_by=self.manager
        )

        status = get_member_payment_status(self.member)
        self.assertEqual(status['status'], 'متأخر')
        self.assertEqual(status['status_class'], 'danger')


class MemberModelTestCase(TestCase):
    """اختبارات نموذج العضو"""

    def setUp(self):
        self.organization = Organization.objects.create(
            name="مؤسسة اختبار",
            description="مؤسسة للاختبار"
        )

        self.member = Member.objects.create(
            organization=self.organization,
            name="عضو اختبار",
            phone="0599123456",
            city="غزة"
        )

    def test_member_str_representation(self):
        """اختبار تمثيل العضو كنص"""
        expected = f"{self.member.name} - {self.organization.name}"
        self.assertEqual(str(self.member), expected)

    def test_get_total_payments_no_payments(self):
        """اختبار إجمالي الدفعات بدون دفعات"""
        total = self.member.get_total_payments()
        self.assertEqual(total, Decimal('0'))

    def test_get_last_payment_no_payments(self):
        """اختبار آخر دفعة بدون دفعات"""
        last_payment = self.member.get_last_payment()
        self.assertIsNone(last_payment)
