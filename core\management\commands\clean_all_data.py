from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from core.models import Organization, Member, Payment, Expense, ActivityLog, Subscription
from core.firebase_config import firebase_service
from django.db import transaction

User = get_user_model()


class Command(BaseCommand):
    help = 'تنظيف جميع البيانات والاحتفاظ بمدير واحد فقط'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='تأكيد حذف جميع البيانات'
        )
        parser.add_argument(
            '--keep-firebase',
            action='store_true',
            help='عدم حذف بيانات Firebase'
        )

    def handle(self, *args, **options):
        if not options['confirm']:
            self.stdout.write(
                self.style.WARNING(
                    'تحذير: هذا الأمر سيحذف جميع البيانات!\n'
                    'لتأكيد الحذف، استخدم: python manage.py clean_all_data --confirm'
                )
            )
            return

        self.stdout.write(
            self.style.WARNING('بدء تنظيف جميع البيانات...')
        )

        try:
            with transaction.atomic():
                # 1. حذف جميع البيانات من Django
                self.clean_django_data()
                
                # 2. إنشاء/تحديث مدير النظام الوحيد
                self.ensure_single_super_admin()
                
            # 3. حذف بيانات Firebase (خارج المعاملة)
            if not options['keep_firebase']:
                self.clean_firebase_data()
            
            self.stdout.write(
                self.style.SUCCESS('تم تنظيف جميع البيانات بنجاح!')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'خطأ في تنظيف البيانات: {str(e)}')
            )

    def clean_django_data(self):
        """حذف جميع البيانات من Django"""
        self.stdout.write('حذف البيانات من قاعدة البيانات المحلية...')
        
        # حذف السجلات بالترتيب الصحيح لتجنب مشاكل المفاتيح الخارجية
        
        # حذف الدفعات
        payments_count = Payment.objects.count()
        Payment.objects.all().delete()
        self.stdout.write(f'  ✓ تم حذف {payments_count} دفعة')
        
        # حذف المصاريف
        expenses_count = Expense.objects.count()
        Expense.objects.all().delete()
        self.stdout.write(f'  ✓ تم حذف {expenses_count} مصروف')
        
        # حذف الأعضاء
        members_count = Member.objects.count()
        Member.objects.all().delete()
        self.stdout.write(f'  ✓ تم حذف {members_count} عضو')
        
        # حذف سجلات النشاط
        activity_logs_count = ActivityLog.objects.count()
        ActivityLog.objects.all().delete()
        self.stdout.write(f'  ✓ تم حذف {activity_logs_count} سجل نشاط')
        
        # حذف الاشتراكات
        subscriptions_count = Subscription.objects.count()
        Subscription.objects.all().delete()
        self.stdout.write(f'  ✓ تم حذف {subscriptions_count} اشتراك')
        
        # حذف المدراء (المستخدمين من نوع manager)
        managers_count = User.objects.filter(user_type='manager').count()
        User.objects.filter(user_type='manager').delete()
        self.stdout.write(f'  ✓ تم حذف {managers_count} مدير')
        
        # حذف المؤسسات (يجب أن يكون آخر شيء)
        organizations_count = Organization.objects.count()
        Organization.objects.all().delete()
        self.stdout.write(f'  ✓ تم حذف {organizations_count} مؤسسة')

    def ensure_single_super_admin(self):
        """التأكد من وجود مدير نظام واحد فقط"""
        self.stdout.write('التأكد من وجود مدير نظام واحد...')
        
        # حذف جميع مدراء النظام الموجودين
        super_admins = User.objects.filter(user_type='super_admin')
        if super_admins.count() > 1:
            # الاحتفاظ بالأول وحذف الباقي
            first_admin = super_admins.first()
            User.objects.filter(user_type='super_admin').exclude(id=first_admin.id).delete()
            self.stdout.write(f'  ✓ تم حذف {super_admins.count() - 1} مدير نظام إضافي')
        
        # إنشاء أو تحديث مدير النظام الوحيد
        admin, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'مدير',
                'last_name': 'النظام',
                'user_type': 'super_admin',
                'organization': None,
                'is_staff': True,
                'is_superuser': True,
                'is_active': True,
                'is_active_user': True,
            }
        )
        
        # تحديث كلمة المرور
        admin.set_password('admin123')
        admin.save()
        
        if created:
            self.stdout.write('  ✓ تم إنشاء مدير النظام الجديد')
        else:
            self.stdout.write('  ✓ تم تحديث مدير النظام الموجود')
        
        self.stdout.write(f'  📧 البريد الإلكتروني: {admin.email}')
        self.stdout.write(f'  🔑 اسم المستخدم: {admin.username}')
        self.stdout.write('  🔒 كلمة المرور: admin123')

    def clean_firebase_data(self):
        """حذف جميع البيانات من Firebase"""
        self.stdout.write('حذف البيانات من Firebase...')
        
        try:
            # التحقق من وجود تكوين Firebase
            if not hasattr(firebase_service, 'db') or firebase_service.db is None:
                self.stdout.write('  ⚠️ Firebase غير مكون، تم تخطي حذف بيانات Firebase')
                return
            
            # حذف جميع المجموعات
            total_deleted = firebase_service.clear_all_collections()
            self.stdout.write(f'  ✓ تم حذف {total_deleted} مستند من Firebase')
            
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'  ⚠️ خطأ في حذف بيانات Firebase: {str(e)}')
            )
            self.stdout.write('  💡 يمكنك حذف البيانات يدوياً من لوحة تحكم Firebase')
