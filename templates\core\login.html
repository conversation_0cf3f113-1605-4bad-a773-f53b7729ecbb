<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة التضامن</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    {% load static %}
    <link href="{% static 'css/style.css' %}" rel="stylesheet">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-logo">
                <i class="fas fa-hands-helping"></i>
                <h2>نظام إدارة التضامن</h2>
                <p class="text-muted">تسجيل الدخول إلى النظام</p>
            </div>
            
            <!-- Messages -->
            {% if messages %}
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endfor %}
            {% endif %}
            
            <!-- Login Form -->
            <form method="post" id="loginForm" data-validate>
                {% csrf_token %}
                
                <div class="mb-3">
                    <label for="{{ form.username.id_for_label }}" class="form-label">
                        <i class="fas fa-user me-2"></i>
                        {{ form.username.label }}
                    </label>
                    {{ form.username }}
                    <small class="form-text text-muted">يمكنك استخدام اسم المستخدم أو البريد الإلكتروني</small>
                    {% if form.username.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.username.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class="mb-4">
                    <label for="{{ form.password.id_for_label }}" class="form-label">
                        <i class="fas fa-lock me-2"></i>
                        {{ form.password.label }}
                    </label>
                    {{ form.password }}
                    {% if form.password.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.password.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        تسجيل الدخول
                    </button>
                </div>
            </form>
            
            <div class="text-center mt-4">
                <div class="mb-2">
                    <small class="text-muted">
                        تحتاج مساعدة في تسجيل الدخول؟
                        <a href="/login-help/" class="text-primary">عرض معلومات تسجيل الدخول</a>
                    </small>
                </div>
                <div>
                    <small class="text-muted">
                        نسيت كلمة المرور؟
                        <a href="#" class="text-primary">اضغط هنا</a>
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    {% load static %}
    <script src="{% static 'js/main.js' %}"></script>
    
    <script>
        // تركيز على حقل البريد الإلكتروني عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const emailField = document.getElementById('{{ form.username.id_for_label }}');
            if (emailField) {
                emailField.focus();
            }
        });
        
        // إضافة تأثيرات بصرية للنموذج
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري تسجيل الدخول...';
            submitBtn.disabled = true;
        });
    </script>
</body>
</html>
