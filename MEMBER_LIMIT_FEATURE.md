# ميزة الحد الأقصى للأعضاء

## نظرة عامة
تم تطبيق ميزة الحد الأقصى للأعضاء في نظام التضامن لتحديد عدد الأعضاء المسموح بهم في كل مؤسسة حسب نوع الاشتراك.

## الميزات المطبقة

### 1. نموذج البيانات
- **Subscription Model**: يحتوي على حقل `max_members` (افتراضي: 50)
- **Organization Model**: تم إضافة دوال مساعدة:
  - `get_active_members_count()`: عدد الأعضاء النشطين
  - `get_max_members_limit()`: الحد الأقصى للأعضاء
  - `can_add_member()`: هل يمكن إضافة عضو جديد
  - `get_remaining_member_slots()`: عدد الأماكن المتبقية

### 2. التحقق من النموذج
- **MemberForm**: يتحقق من الحد الأقصى قبل إضافة عضو جديد
- **رسالة الخطأ**: "لقد وصلت الحد الاقصى لعدد الاعضاء الرجاء التواصل مع مالك المشروع على الرقم 0598455262"

### 3. واجهة المستخدم

#### صفحة قائمة الأعضاء
- عرض إحصائيات الأعضاء (الحالي/الأقصى/المتبقي)
- تعطيل زر "إضافة عضو جديد" عند الوصول للحد الأقصى
- تنبيهات ملونة حسب الحالة:
  - **أخضر**: متاح للإضافة
  - **أصفر**: قريب من الامتلاء (3 أماكن أو أقل)
  - **أحمر**: مكتمل

#### صفحة إضافة عضو
- عرض إحصائيات مفصلة قبل الإضافة
- منع الوصول للصفحة عند الوصول للحد الأقصى
- تنبيهات تحذيرية

### 4. لوحة الإدارة
- عرض معلومات الحد الأقصى في قائمة الاشتراكات
- عرض عدد الأعضاء الحاليين والأماكن المتبقية
- إمكانية تعديل الحد الأقصى من لوحة الإدارة

## الأوامر المتاحة

### تحديد الحد الأقصى للأعضاء
```bash
# تحديد الحد لجميع المؤسسات
python manage.py set_member_limit --all-organizations --max-members 10

# تحديد الحد لمؤسسة محددة بالمعرف
python manage.py set_member_limit --organization-id 1 --max-members 5

# تحديد الحد لمؤسسة محددة بالاسم
python manage.py set_member_limit --organization-name "جمعية الخير" --max-members 15
```

### تنظيف البيانات
```bash
# تنظيف جميع البيانات والاحتفاظ بمدير واحد
python manage.py clean_all_data --confirm

# تنظيف البيانات مع الاحتفاظ ببيانات Firebase
python manage.py clean_all_data --confirm --keep-firebase
```

## سيناريوهات الاستخدام

### 1. إضافة عضو جديد
1. المدير يدخل لصفحة قائمة الأعضاء
2. يرى إحصائيات الأعضاء الحالية
3. إذا كان هناك أماكن متاحة، يمكنه الضغط على "إضافة عضو جديد"
4. إذا وصل للحد الأقصى، يظهر زر معطل مع رسالة

### 2. الوصول للحد الأقصى
1. عند محاولة إضافة عضو والوصول للحد الأقصى
2. يظهر تنبيه أحمر مع رقم التواصل
3. يتم منع إضافة العضو
4. يُنصح بالتواصل مع مالك المشروع

### 3. قرب الامتلاء
1. عند تبقي 3 أماكن أو أقل
2. يظهر تنبيه أصفر تحذيري
3. يمكن الإضافة لكن مع تحذير

## رقم التواصل
**مالك المشروع**: 0598455262

## ملاحظات تقنية
- الحد الأقصى يُطبق على الأعضاء النشطين فقط (`is_active=True`)
- الأعضاء المحذوفين أو غير النشطين لا يُحسبون في العدد
- التحقق يتم على مستوى النموذج والعرض
- يمكن تعديل الحد الأقصى من لوحة الإدارة في أي وقت

## الاختبار
تم اختبار الميزة بنجاح مع:
- إضافة أعضاء حتى الوصول للحد الأقصى
- التحقق من رسائل الخطأ
- واجهة المستخدم والتنبيهات
- لوحة الإدارة

## التطوير المستقبلي
- إمكانية تحديد حدود مختلفة حسب نوع الاشتراك
- تنبيهات بريد إلكتروني عند قرب الامتلاء
- إحصائيات مفصلة في لوحة التحكم
